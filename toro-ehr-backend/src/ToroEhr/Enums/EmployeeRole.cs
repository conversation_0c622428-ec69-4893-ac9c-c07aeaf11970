using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class EmployeeRole : SmartEnum<EmployeeRole, string>
{
    public static readonly EmployeeRole OrganizationAdmin = new EmployeeRole(nameof(OrganizationAdmin), "Organization Admin");
    public static readonly EmployeeRole LocationAdmin = new EmployeeRole(nameof(LocationAdmin), "Location Admin");
    public static readonly EmployeeRole Practitioner = new EmployeeRole(nameof(Practitioner), "Practitioner");
    public static readonly EmployeeRole Nurse = new EmployeeRole(nameof(Nurse), "Nurse");
    public static readonly EmployeeRole FrontDesk = new EmployeeRole(nameof(FrontDesk), "Front Desk");

    private EmployeeRole(string name, string value) : base(name, value)
    {
    }
}
