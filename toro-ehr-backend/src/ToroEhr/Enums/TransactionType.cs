using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public class TransactionType : SmartEnum<TransactionType, string>
{
    public static readonly TransactionType Charge = new TransactionType(nameof(Charge), nameof(Charge));
    public static readonly TransactionType Refund = new TransactionType(nameof(Refund), nameof(Refund));
    public static readonly TransactionType Void = new TransactionType(nameof(Void), nameof(Void));

    public TransactionType(string name, string value) : base(name, value)
    {
    }
}