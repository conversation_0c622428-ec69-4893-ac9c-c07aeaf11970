using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class MeasurementType : SmartEnum<MeasurementType, string>
{
    public static readonly MeasurementType BodyTemperature = new MeasurementType(nameof(BodyTemperature), "Body temperature", "8310-5", "C", "Decimal");
    public static readonly MeasurementType InhaledOxygenConcentration = new MeasurementType(nameof(InhaledOxygenConcentration), "Inhaled oxygen concentration", "3150-0", "%", "Decimal");
    public static readonly MeasurementType InhaledOxygenFlowRate = new MeasurementType(nameof(InhaledOxygenFlowRate), "Inhaled oxygen flow rate", "3151-8", "L/min", "Decimal");
    public static readonly MeasurementType RespiratoryRate = new MeasurementType(nameof(RespiratoryRate), "Respiratory rate", "9279-1", "breaths/min", "Decimal");
    public static readonly MeasurementType HeartRate = new MeasurementType(nameof(HeartRate), "Heart rate", "8867-4", "bpm", "Decimal");
    public static readonly MeasurementType OxygenSaturation = new MeasurementType(nameof(OxygenSaturation), "Oxygen saturation in Arterial blood", "2708-6", "%", "Decimal");
    public static readonly MeasurementType BodyHeight = new MeasurementType(nameof(BodyHeight), "Body height", "8302-2", "cm", "Decimal");
    public static readonly MeasurementType HeadCircumference = new MeasurementType(nameof(HeadCircumference), "Head Occipital-frontal circumference", "9843-4", "cm", "Decimal");
    public static readonly MeasurementType BodyWeight = new MeasurementType(nameof(BodyWeight), "Body weight", "29463-7", "kg", "Decimal");
    public static readonly MeasurementType BmiRatio = new MeasurementType(nameof(BmiRatio), "Body mass index (BMI) [Ratio]", "39156-5", "kg/m²", "Decimal");
    public static readonly MeasurementType SystolicBloodPressure = new MeasurementType(nameof(SystolicBloodPressure), "Systolic blood pressure", "8480-6", "mmHg", "Decimal");
    public static readonly MeasurementType DiastolicBloodPressure = new MeasurementType(nameof(DiastolicBloodPressure), "Diastolic blood pressure", "8462-4", "mmHg", "Decimal");
    public static readonly MeasurementType PainScale = new MeasurementType(nameof(PainScale), "Pain scale", "80316-3", "0-10", "Decimal");

    public string Code { get; set; }
    public string Unit { get; set; }
    public string ValueType { get; set; }

    private MeasurementType(string name, string value, string code, string unit, string valueType) : base(name, value)
    {
        Code = code;
        Unit = unit;
        ValueType = valueType;
    }
}