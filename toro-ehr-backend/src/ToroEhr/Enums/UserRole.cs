using Ardalis.SmartEnum;

namespace ToroEhr.Enums
{
    public sealed class UserRole : SmartEnum<UserRole, string>
    {
        public static readonly UserRole SuperAdmin = new UserRole(nameof(SuperAdmin), "Super Admin");
        public static readonly UserRole Employee = new UserRole(nameof(Employee), "Employee");
        public static readonly UserRole Patient = new UserRole(nameof(Patient), "Patient");

        private UserRole(string name, string value) : base(name, value)
        {
        }
    }
}
