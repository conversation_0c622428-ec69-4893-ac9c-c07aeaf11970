using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class OrderPriorityType : SmartEnum<OrderPriorityType, string>
{
    public static readonly OrderPriorityType Asap = new OrderPriorityType(nameof(Asap), "Asap");
    public static readonly OrderPriorityType Stat = new OrderPriorityType(nameof(Stat), "Stat");
    public static readonly OrderPriorityType Routine = new OrderPriorityType(nameof(Routine), "Routine");
    public static readonly OrderPriorityType Urgent = new OrderPriorityType(nameof(Urgent), "Urgent");
    private OrderPriorityType(string name, string value) : base(name, value)
    {
    }
}