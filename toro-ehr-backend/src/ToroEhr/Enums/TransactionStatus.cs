using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public class TransactionStatus : SmartEnum<TransactionStatus, string>
{
    public static readonly TransactionStatus Approved = new TransactionStatus(nameof(Approved), nameof(Approved));
    public static readonly TransactionStatus Declined = new TransactionStatus(nameof(Declined), nameof(Declined));
    public static readonly TransactionStatus Voided = new TransactionStatus(nameof(Voided), nameof(Voided));
    public static readonly TransactionStatus Refunded = new TransactionStatus(nameof(Refunded), nameof(Refunded));

    public TransactionStatus(string name, string value) : base(name, value)
    {
    }
}