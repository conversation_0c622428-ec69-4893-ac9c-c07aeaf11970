using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public class LaboratoryResultStatus : SmartEnum<LaboratoryResultStatus, string>
{
    public static readonly LaboratoryResultStatus Registered = new LaboratoryResultStatus(nameof(Registered), "Registered");
    public static readonly LaboratoryResultStatus Preliminary = new LaboratoryResultStatus(nameof(Preliminary), "Preliminary");
    public static readonly LaboratoryResultStatus Final = new LaboratoryResultStatus(nameof(Final), "Final");
    public static readonly LaboratoryResultStatus Amended = new LaboratoryResultStatus(nameof(Amended), "Amended");

    private LaboratoryResultStatus(string name, string value) : base(name, value)
    {
    }
}

