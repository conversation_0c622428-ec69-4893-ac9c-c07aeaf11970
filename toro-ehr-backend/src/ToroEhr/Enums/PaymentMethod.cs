using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public class PaymentMethod : SmartEnum<PaymentMethod, string>
{
    public static readonly PaymentMethod PosTerminal = new PaymentMethod(nameof(PosTerminal), nameof(PosTerminal));
    public static readonly PaymentMethod SavedCard = new PaymentMethod(nameof(SavedCard), "Saved Card");

    public PaymentMethod(string name, string value) : base(name, value)
    {
    }
}