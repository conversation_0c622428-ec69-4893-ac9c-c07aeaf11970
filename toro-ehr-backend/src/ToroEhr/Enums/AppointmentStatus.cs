using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class AppointmentStatus : SmartEnum<AppointmentStatus, string>
{
    public static readonly AppointmentStatus Confirmed = new AppointmentStatus(nameof(Confirmed), "Confirmed");
    public static readonly AppointmentStatus Pending = new AppointmentStatus(nameof(Pending), "Pending");
    public static readonly AppointmentStatus Canceled = new AppointmentStatus(nameof(Canceled), "Canceled");
    public static readonly AppointmentStatus Missed = new AppointmentStatus(nameof(Missed), "Missed");
    public static readonly AppointmentStatus CheckedIn = new AppointmentStatus(nameof(CheckedIn), "Checked In");

    private AppointmentStatus(string name, string value) : base(name, value)
    {
    }
}