using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class OrderEntryType : SmartEnum<OrderEntryType, string>
{
    public static readonly OrderEntryType Procedure = new OrderEntryType(nameof(Procedure), "Procedure");
    public static readonly OrderEntryType Med = new OrderEntryType(nameof(Med), "Medicine");
    public static readonly OrderEntryType Lab = new OrderEntryType(nameof(Lab), "Lab");
    private OrderEntryType(string name, string value) : base(name, value)
    {
    }
}
