using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class MessageType : SmartEnum<MessageType, string>
{
    public static readonly MessageType Email = new MessageType(nameof(Email), "Email");
    public static readonly MessageType Sms = new MessageType(nameof(Sms), "Sms");
    public static readonly MessageType Call = new MessageType(nameof(Call), "Call");

    private MessageType(string name, string value) : base(name, value)
    {
    }
}