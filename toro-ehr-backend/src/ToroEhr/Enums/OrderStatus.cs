using Ardalis.SmartEnum;

namespace ToroEhr.Enums;

public sealed class OrderStatus : SmartEnum<OrderStatus, string>
{
    public static readonly OrderStatus Draft = new OrderStatus(nameof(Draft), "Draft");
    public static readonly OrderStatus Active = new OrderStatus(nameof(Active), "Active");
    public static readonly OrderStatus OnHold = new OrderStatus(nameof(OnHold), "On Hold");
    public static readonly OrderStatus Revoked = new OrderStatus(nameof(Revoked), "Revoked");
    public static readonly OrderStatus Completed = new OrderStatus(nameof(Completed), "Completed");
    public static readonly OrderStatus EnteredInError = new OrderStatus(nameof(EnteredInError), "Entered in Error");
    public static readonly OrderStatus Unknown = new OrderStatus(nameof(Unknown), "Unknown");
    private OrderStatus(string name, string value) : base(name, value)
    {
    }
}