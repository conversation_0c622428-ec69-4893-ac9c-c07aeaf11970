using Newtonsoft.Json;

namespace ToroEhr.ValueObjects;

public sealed class PhoneNumber
{
    [JsonConstructor]
    private PhoneNumber(string number, string type, bool isPrimary)
    {
        Number = number;
        Type = type;
        IsPrimary = isPrimary;
    }

    public string Number { get; private set; }

    public string Type { get; private set; }

    public bool IsPrimary { get; private set; }
    
    public static PhoneNumber Create(string number, string type, bool isPrimary) => new(number, type, isPrimary);
    
}