using Newtonsoft.Json;

namespace ToroEhr.ValueObjects;

public sealed class EmailAddress
{
    [JsonConstructor]
    private EmailAddress(string email, bool isPrimary)
    {
        Email = email;
        IsPrimary = isPrimary;
    }

    public string Email { get; private set; }

    public bool IsPrimary { get; private set; }
    
    public static EmailAddress Create(string email, bool isPrimary) => new(email, isPrimary);
    
}