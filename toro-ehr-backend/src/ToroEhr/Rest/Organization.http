@ToroEhr_HostAddress = http://localhost:5233

### Create organization
POST {{ToroEhr_HostAddress}}/organizations/
Content-Type: application/json

{
  "name": "Organization Test"
}

### Update organization
PUT {{Toro<PERSON>hr_HostAddress}}/organizations/{id}
Content-Type: application/json

{
  "id": "bc948b0c-3d63-4af9-860e-2afc3b9443be",
  "name": "Organization Test 1"
}

### Find organization
GET {{ToroEhr_HostAddress}}/carriers/214ba169-63e8-4f04-9e7a-f239679ffe25
Accept: application/json

### List organizations
GET {{ToroEhr_HostAddress}}/organizations?searchParam=test
Accept: application/json
