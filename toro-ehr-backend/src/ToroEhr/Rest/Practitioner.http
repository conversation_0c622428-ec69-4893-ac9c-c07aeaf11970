@ToroEhr_HostAddress = http://localhost:5233

### Create employee
POST {{<PERSON><PERSON><PERSON>_HostAddress}}/employees/
Authorization: Bearer GQytp8dH4Icd2wYMZZSgOFqAveUQ6lTgImuEuXyBnqSoUrGDxjJwGrSExxYwF7hbQBw2iadV5v1GqqUn51AC3g==
Content-Type: application/json

{
  "organizationId": "01JHMZPDKZN7ATJTJJ9M21MYY4",
  "roles": ["Doctor", "Admin"],
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Doe",
  "npi": "**********",
  "phone": "+**********",
  "timeZone": "America/New_York",
  "calendarColor": "#3498db",
  "address": {
    "street": "123 Elm Street",
    "city": "Springfield",
    "state": "NY",
    "zipCode": "12345"
  }
}

### List employees
GET {{<PERSON><PERSON><PERSON>_HostAddress}}/employees
Authorization: Bearer GQytp8dH4Icd2wYMZZSgOFqAveUQ6lTgImuEuXyBnqSoUrGDxjJwGrSExxYwF7hbQBw2iadV5v1GqqUn51AC3g==
Accept: application/json
