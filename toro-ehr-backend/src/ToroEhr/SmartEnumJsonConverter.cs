using Ardalis.SmartEnum;
using Newtonsoft.Json;
using NJsonSchema;
using NJsonSchema.Generation;

namespace ToroEhr;

public class SmartEnumJsonConverter<TEnum> : JsonConverter<TEnum> where TEnum : SmartEnum<TEnum, int>
{
    public override bool CanRead => true;

    public override void WriteJ<PERSON>(JsonWriter writer, TEnum? value, JsonSerializer serializer)
    {
        if (value != null) writer.WriteValue(value.Value);
    }

    public override TEnum? Read<PERSON>son(JsonReader reader, Type objectType, TEnum? existingValue,
        bool hasExistingValue,
        JsonSerializer serializer)
    {
        var intValue = reader.Value as int? ?? int.Parse(reader.Value.ToString());
        return SmartEnum<TEnum, int>.FromValue(intValue);
    }
}

public class SmartEnumSchemaProcessor<TEnum> : ISchemaProcessor
    where TEnum : SmartEnum<TEnum, string>
{
    public void Process(SchemaProcessorContext context)
    {
        if (context.Type == typeof(TEnum))
        {
            var schema = context.Schema;
            schema.Type = JsonObjectType.String;
            schema.Enumeration.Clear();

            foreach (var e in SmartEnum<TEnum, string>.List)
            {
                schema.Enumeration.Add(e.Value); // e.Value is string, can contain spaces
            }

            schema.Description ??= typeof(TEnum).Name;
        }
    }
}