using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Questionnaires_ByLocation : AbstractIndexCreationTask<Questionnaire, Questionnaires_ByLocation.Entry>
{
    public class Entry
    {
        public string QuestionnaireId { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Type { get; set; } = null!;
        public string Placement { get; set; } = null!;
        public string OrganizationId { get; set; } = null!;
        public string LocationId { get; set; } = null!;
        public string LocationName { get; set; } = null!;
        public string OrganizationName { get; set; } = null!;
        public string[] SearchParams { get; set; } = null!;
    }

    public Questionnaires_ByLocation()
    {
        Map = questionnaires =>
            from questionnaire in questionnaires
            let location = LoadDocument<Location>(questionnaire.LocationId)
            let organization = LoadDocument<Organization>(location.OrganizationId)
            select new Entry
            {
                QuestionnaireId = questionnaire.Id,
                Title = questionnaire.Title,
                Type = questionnaire.Type,
                Placement = questionnaire.Placement,
                OrganizationId = organization.Id,
                LocationId = questionnaire.LocationId,
                LocationName = location.Name,
                OrganizationName = organization.Name,
                SearchParams = new[]
                {
                    questionnaire.Title,
                    questionnaire.Type,
                    location.Name,
                    organization.Name,
                }
            };

        Stores.Add(x => x.QuestionnaireId, FieldStorage.Yes);
        Stores.Add(x => x.Title, FieldStorage.Yes);
        Stores.Add(x => x.Type, FieldStorage.Yes);
        Stores.Add(x => x.Placement, FieldStorage.Yes);
        Stores.Add(x => x.LocationName, FieldStorage.Yes);
        Stores.Add(x => x.OrganizationName, FieldStorage.Yes);
    }
}