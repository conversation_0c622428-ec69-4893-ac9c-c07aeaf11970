using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Orders_ByEncounter : AbstractMultiMapIndexCreationTask<Orders_ByEncounter.Entry>
{
    public class Entry
    {
        public string OrderId { get; set; } = null!;
        public string OrderType { get; set; } = null!;
        public string? IngredientId { get; set; } = null!;
        public string? LoincCodeId { get; set; } = null!;
        public string? SnomedCodeId { get; set; } = null!;
        public string? EncounterId { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string PractitionerId { get; set; } = null!;
        public string Status { get; set; } = null!;
    }

    public Orders_ByEncounter()
    {
        AddMap<OrderMedication>(orderMedications =>
            from orderMedication in orderMedications
            let encounter = LoadDocument<Encounter>(orderMedication.EncounterId)
            select new Entry
            {
                OrderId = orderMedication.Id,
                OrderType = nameof(OrderMedication),
                IngredientId = orderMedication.IngredientId,
                LoincCodeId = null,
                SnomedCodeId = null,
                EncounterId = orderMedication.EncounterId,
                PatientId = orderMedication.PatientId,
                Status = orderMedication.Status,
                PractitionerId = encounter.PractitionerId,
            });
        AddMap<OrderLab>(orderLabs =>
            from orderLab in orderLabs
            let encounter = LoadDocument<Encounter>(orderLab.EncounterId)
            select new Entry
            {
                OrderId = orderLab.Id,
                OrderType = nameof(OrderLab),
                IngredientId = null,
                LoincCodeId = orderLab.LoincCodeId,
                SnomedCodeId = null,
                EncounterId = orderLab.EncounterId,
                PatientId = orderLab.PatientId,
                Status = orderLab.Status,
                PractitionerId= encounter.PractitionerId,
            });
        AddMap<OrderProcedure>(orderProcedures =>
            from orderProcedure in orderProcedures
            let encounter = LoadDocument<Encounter>(orderProcedure.EncounterId)
            select new Entry
            {
                OrderId = orderProcedure.Id,
                OrderType = nameof(OrderProcedure),
                IngredientId = null,
                LoincCodeId = null,
                SnomedCodeId = orderProcedure.SnomedCodeId,
                EncounterId = orderProcedure.EncounterId,
                PatientId = orderProcedure.PatientId,
                Status = orderProcedure.Status,
                PractitionerId = encounter.PractitionerId,
            });
        AddMap<OrderBundle>(orderBundles =>
            from orderBundle in orderBundles
            let encounter = LoadDocument<Encounter>(orderBundle.EncounterId)
            select new Entry
            {
                OrderId = orderBundle.Id,
                OrderType = nameof(OrderBundle),
                IngredientId = null,
                LoincCodeId = null,
                SnomedCodeId = null,
                EncounterId = orderBundle.EncounterId,
                PatientId = orderBundle.PatientId,
                Status = orderBundle.Status,
                PractitionerId= encounter.PractitionerId,
            });

        Stores.Add(x => x.OrderId, FieldStorage.Yes);
        Stores.Add(x => x.OrderType, FieldStorage.Yes);
        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.Status, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerId, FieldStorage.Yes);
    }
}