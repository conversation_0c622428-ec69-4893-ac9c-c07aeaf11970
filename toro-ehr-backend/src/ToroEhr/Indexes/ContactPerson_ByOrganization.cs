using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class ContactPerson_ByOrganization : AbstractIndexCreationTask<LocationEmployee, ContactPerson_ByOrganization.Entry>
{
    public class Entry
    {
        public string EmployeeId { get; set; } = null!;

        public string OrganizationId { get; set; } = null!;

        public string[] SearchParams { get; set; } = null!;
    }

    public ContactPerson_ByOrganization()
    {
        Map = locationEmployees =>
        from locationEmployee in locationEmployees
        where locationEmployee.EmployeeRoles.Contains("OrganizationAdmin")
        let organization = LoadDocument<Organization>(locationEmployee.OrganizationId)
        select new Entry
        {
            EmployeeId = locationEmployee.EmployeeId,
            OrganizationId = locationEmployee.OrganizationId,
            SearchParams = new[]
                {
                    organization.Name
                }
        };

        Reduce = results => from result in results
                            group result by new { result.OrganizationId, result.SearchParams }
            into g
                            select new Entry
                            {
                                EmployeeId = g.First().EmployeeId,
                                OrganizationId = g.Key.OrganizationId,
                                SearchParams = g.Key.SearchParams
                            };

        Stores.Add(x => x.EmployeeId, FieldStorage.Yes);
        Stores.Add(x => x.OrganizationId, FieldStorage.Yes);
        Analyzers.Add(n => n.SearchParams, "LowerCaseWhitespaceAnalyzer");
    }
}
