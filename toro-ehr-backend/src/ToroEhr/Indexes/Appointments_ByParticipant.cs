using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Appointments_ByParticipant : AbstractIndexCreationTask<Appointment, Appointments_ByParticipant.Entry>
{
    public class Entry
    {
        public string AppointmentId { get; set; } = null!;
        public string EncounterId { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string EmployeeId { get; set; } = null!;
        public string EmployeeFullName { get; set; } = null!;
        public string LocationName { get; set; } = null!;
        public DateTimeOffset? StartAt { get; set; }
        public int CheckInStartOffsetHours { get; set; }
        public decimal MissedAppointmentFeeInCents { get; set; }
    }
    
    public Appointments_ByParticipant()
    {
        Map = appointments =>
            from appointment in appointments
            let employee = LoadDocument<Employee>(appointment.EmployeeId)
            let location = LoadDocument<Location>(appointment.LocationId)
            select new Entry
            {
                AppointmentId = appointment.Id,
                EncounterId = appointment.EncounterId,
                PatientId = appointment.PatientId,
                EmployeeId = employee.Id,
                EmployeeFullName = $"{employee.FirstName} {employee.LastName}",
                LocationName = location.Name,
                StartAt = appointment.StartAt,
                CheckInStartOffsetHours = location.CheckInStartOffsetHours,
                MissedAppointmentFeeInCents = location.MissedFeeInCents
            };
        
        Stores.Add(x => x.AppointmentId, FieldStorage.Yes);
        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.EmployeeFullName, FieldStorage.Yes);
        Stores.Add(x => x.LocationName, FieldStorage.Yes);
        Stores.Add(x => x.CheckInStartOffsetHours, FieldStorage.Yes);
        Stores.Add(x => x.MissedAppointmentFeeInCents, FieldStorage.Yes);
    }
}