using DocumentFormat.OpenXml.Wordprocessing;
using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class OrderEntrySearch : AbstractMultiMapIndexCreationTask<OrderEntrySearch.Entry>
{
    public class Entry
    {
        public string Type { get; set; } = null!;
        public string Id { get; set; } = null!;
        public string Code { get; set; } = null!;
        public string DisplayName { get; set; } = null!;
        public string[] SearchParams { get; set; } = null!;
    }

    public OrderEntrySearch()
    {
        AddMap<Medication>(medications =>
            from medication in medications
            where medication.SourceAbbreviation == "RXNORM" && 
            (medication.TermType == "IN" || medication.TermType == "MIN")
            select new Entry
            {
                Type = "Med",
                Id = medication.Id,
                Code = medication.RxCui,
                DisplayName = medication.TermString,
                SearchParams = new[]
                {
                        medication.RxCui,
                        medication.TermString,
                }
            });
        AddMap<LoincCode>(loincCodes =>
            from loincCode in loincCodes
            select new Entry
            {
                Type = "Lab",
                Id = loincCode.Id,
                Code = loincCode.LoincNum ?? string.Empty,
                DisplayName = loincCode.DisplayName ?? string.Empty,
                SearchParams = new[]
                {
                        loincCode.Id,
                        loincCode.DisplayName ?? string.Empty,
                }
            });
        AddMap<OrderBundleTemplate>(bundles =>
            from bundle in bundles
            select new Entry
            {
                Type = "Bundle",
                Id = bundle.Id,
                Code = string.Empty,
                DisplayName = bundle.Name,
                SearchParams = new[]
                {
                    bundle.Name,
                }
            });
        AddMap<SnomedCode>(snomedCodes =>
        from snomedCode in snomedCodes
            where snomedCode.Term != null && snomedCode.IsActive && snomedCode.IsProcedure
            select new Entry
            {
                Type = "Procedure",
                Id = snomedCode.Id,
                Code = snomedCode.SnomedId,
                DisplayName = snomedCode.Term,
                SearchParams = new[]
                {
                    snomedCode.Term,
                }
            });

        Stores.Add(x => x.Type, FieldStorage.Yes);
        Stores.Add(x => x.Id, FieldStorage.Yes);
        Stores.Add(x => x.Code, FieldStorage.Yes);
        Stores.Add(x => x.DisplayName, FieldStorage.Yes);
        Analyzers.Add(n => n.SearchParams, "LowerCaseWhitespaceAnalyzer");
    }
}
