using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Communication_ByEncounter : AbstractMultiMapIndexCreationTask<Communication_ByEncounter.Entry>
{
    public class Entry
    {
        public string Id { get; set; } = null!;
        public string PatientId { get; set; } = null!;
        public string PractitionerId { get; set; } = null!;
        public string? EncounterId { get; set; } = null!;
        public string Subject { get;  set; } = null!;
        public string Message { get;  set; } = null!;
        public string Type { get;  set; } = null!;
        public List<string> Attachments { get;  set; } = null!;
        public DateTimeOffset SentAt { get; set; }
    }
    public Communication_ByEncounter()
    {
        AddMap<EncounterEmail>(encounterEmails =>
            from encounterEmail in encounterEmails
            let encounter = LoadDocument<Encounter>(encounterEmail.EncounterId)
            select new Entry
            {
                Id = encounterEmail.Id,
                PatientId = encounterEmail.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounterEmail.EncounterId,
                Subject = encounterEmail.Subject,
                Message = encounterEmail.Message,
                Attachments = encounterEmail.Attachments,
                Type = "Email",
                SentAt = encounterEmail.SentAt,
            });
        AddMap<EncounterSms>(encounterSmses =>
            from encounterSms in encounterSmses
            let encounter = LoadDocument<Encounter>(encounterSms.EncounterId)
            select new Entry
            {
                Id = encounterSms.Id,
                PatientId = encounterSms.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounterSms.EncounterId,
                Subject = encounterSms.Subject,
                Message = encounterSms.Message,
                Attachments = new List<string>(),
                Type = "Text",
                SentAt = encounterSms.SentAt,
            });
        AddMap<EncounterCallRecord>(encounterCalls =>
            from encounterCall in encounterCalls
            let encounter = LoadDocument<Encounter>(encounterCall.EncounterId)
            select new Entry
            {
                Id = encounterCall.Id,
                PatientId = encounterCall.PatientId,
                PractitionerId = encounter.PractitionerId,
                EncounterId = encounterCall.EncounterId,
                Subject = encounterCall.Subject,
                Message = encounterCall.Message,
                Attachments = new List<string>(),
                Type = "Call",
                SentAt = encounterCall.SentAt,
            });

        Reduce = results => from result in results
                            group result by new { result.Id, result.PatientId, result.PractitionerId, result.EncounterId, result.Subject, result.Message, result.Attachments, 
                                result.Type, result.SentAt }
            into g
                            select new Entry
                            {
                                Id = g.Key.Id,
                                PatientId = g.Key.PatientId,
                                PractitionerId = g.Key.PractitionerId,
                                EncounterId = g.Key.EncounterId,
                                Subject = g.Key.Subject,
                                Message = g.Key.Message,
                                Attachments = g.Key.Attachments,
                                Type = g.Key.Type,
                                SentAt = g.Key.SentAt,
                            };

        Stores.Add(x => x.Id, FieldStorage.Yes);
        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.PractitionerId, FieldStorage.Yes);
        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.Subject, FieldStorage.Yes);
        Stores.Add(x => x.Message, FieldStorage.Yes);
        Stores.Add(x => x.Attachments, FieldStorage.Yes);
        Stores.Add(x => x.Type, FieldStorage.Yes);
        Stores.Add(x => x.SentAt, FieldStorage.Yes);
    }

}
