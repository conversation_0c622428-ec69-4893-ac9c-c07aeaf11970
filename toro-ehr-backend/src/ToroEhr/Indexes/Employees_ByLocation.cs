using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes
{
    public class Employees_ByLocation : AbstractMultiMapIndexCreationTask<Employees_ByLocation.Entry>
    {
        public class Entry
        {
            public string EmployeeId { get; set; } = null!;

            public string OrganizationId { get; set; } = null!;

            public string LocationId { get; set; } = null!;

            public string FirstName { get; set; } = null!;

            public string LastName { get; set; } = null!;

            public string CalendarColor { get; set; } = null!;

            public string TimeZone { get; set; } = null!;

            public List<string> Roles { get; set; } = null!;

            public string[] SearchParams { get; set; } = null!;
        }

        public Employees_ByLocation()
        {
            AddMap<LocationEmployee>(orgEmployees =>
                from orgEmployee in orgEmployees
                let employee = LoadDocument<Employee>(orgEmployee.EmployeeId)
                let organization = LoadDocument<Organization>(orgEmployee.OrganizationId)
                select new Entry
                {
                    EmployeeId = employee.Id,
                    OrganizationId = organization.Id,
                    LocationId = orgEmployee.LocationId,
                    FirstName = employee.FirstName,
                    LastName = employee.LastName,
                    CalendarColor = orgEmployee.CalendarColor,
                    TimeZone = orgEmployee.TimeZone,
                    Roles = orgEmployee.EmployeeRoles,
                    SearchParams = new[]
                    {
                        employee.Email,
                        employee.FirstName,
                        employee.LastName,
                    }
                });

            Reduce = results => from result in results
                group result by new { result.EmployeeId, result.OrganizationId, result.LocationId, result.FirstName, result.LastName, 
                    result.CalendarColor, result.TimeZone, result.Roles }
                into g
                select new Entry
                {
                    EmployeeId = g.Key.EmployeeId,
                    OrganizationId = g.Key.OrganizationId,
                    LocationId = g.Key.LocationId,
                    FirstName = g.Key.FirstName,
                    LastName = g.Key.LastName,
                    CalendarColor = g.Key.CalendarColor,
                    TimeZone = g.Key.TimeZone,
                    Roles = g.Key.Roles,
                    SearchParams = g.SelectMany(gr => gr.SearchParams).Distinct().ToArray()
                };

            Stores.Add(x => x.EmployeeId, FieldStorage.Yes);
            Stores.Add(x => x.OrganizationId, FieldStorage.Yes);
            Stores.Add(x => x.LocationId, FieldStorage.Yes);
            Stores.Add(x => x.FirstName, FieldStorage.Yes);
            Stores.Add(x => x.LastName, FieldStorage.Yes);
            Stores.Add(x => x.CalendarColor, FieldStorage.Yes);
            Stores.Add(x => x.TimeZone, FieldStorage.Yes);
            Stores.Add(x => x.Roles, FieldStorage.Yes);
            Analyzers.Add(n => n.SearchParams, "LowerCaseWhitespaceAnalyzer");
        }
    }
}
