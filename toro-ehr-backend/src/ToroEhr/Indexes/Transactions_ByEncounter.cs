using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Transactions_ByEncounter : AbstractMultiMapIndexCreationTask<Transactions_ByEncounter.Entry>
{
    public class Entry
    {
        public string EncounterId { get; set; } = null!;

        public string LocationId { get; set; } = null!;

        public List<string> PaymentTransactions { get; set; } = null!;

        public decimal ChargedAmount { get; set; }

        public decimal RefundedAmount { get; set; }

        public string PatientName { get; set; } = null!;

        public string? PatientPhone { get; set; }

        public DateTimeOffset EncounterDate { get; set; }

        public string PaymentStatus { get; set; } = null!;

        public string BusinessStatus { get; set; } = null!;
    }

    public Transactions_ByEncounter()
    {
        AddMap<PaymentTransaction>(transactions =>
            from transaction in transactions
            let encounter = LoadDocument<Encounter>(transaction.EncounterId)
            let patient = LoadDocument<Patient>(transaction.PatientId)
            select new Entry
            {
                EncounterId = transaction.EncounterId,
                LocationId = encounter.LocationId,
                PaymentTransactions = new List<string> { transaction.Id },
                ChargedAmount = transaction.Type == "Charge" ? transaction.Amount : 0,
                RefundedAmount = transaction.Type == "Refund" || transaction.Type == "Void" ? transaction.Amount : 0,
                PatientName = $"{patient.FirstName!} {patient.LastName!}",
                PatientPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                EncounterDate = encounter.StartAt,
                PaymentStatus = encounter.PaymentStatus,
                BusinessStatus = encounter.Status,
            });

        AddMap<Encounter>(encounters =>
            from encounter in encounters
            let patient = LoadDocument<Patient>(encounter.PatientId)
            where encounter.Status != "Canceled"
            select new Entry
            {
                EncounterId = encounter.Id,
                LocationId = encounter.LocationId,
                PaymentTransactions = new List<string>(),
                ChargedAmount = 0,
                RefundedAmount = 0,
                PatientName = $"{patient.FirstName} {patient.LastName}",
                PatientPhone = patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary).Number,
                EncounterDate = encounter.StartAt,
                PaymentStatus = encounter.PaymentStatus,
                BusinessStatus = encounter.Status,
            });

        Reduce = results => from result in results
            group result by new { result.EncounterId }
            into g
            select new Entry
            {
                EncounterId = g.Key.EncounterId,
                LocationId = g.First().LocationId,
                PaymentTransactions = g.SelectMany(gr => gr.PaymentTransactions).ToList(),
                ChargedAmount = g.Sum(x => x.ChargedAmount),
                RefundedAmount = g.Sum(x => x.RefundedAmount),
                PatientName = g.First().PatientName,
                PatientPhone = g.First().PatientPhone,
                EncounterDate = g.First().EncounterDate,
                PaymentStatus = g.Any(x => x.PaymentStatus == "Paid") ? "Paid" : "Unpaid",
                BusinessStatus = g.First().BusinessStatus,
            };

        Stores.Add(x => x.EncounterId, FieldStorage.Yes);
        Stores.Add(x => x.PaymentTransactions, FieldStorage.Yes);
        Stores.Add(x => x.ChargedAmount, FieldStorage.Yes);
        Stores.Add(x => x.RefundedAmount, FieldStorage.Yes);
        Stores.Add(x => x.PatientName, FieldStorage.Yes);
        Stores.Add(x => x.PatientPhone, FieldStorage.Yes);
        Stores.Add(x => x.EncounterDate, FieldStorage.Yes);
        Stores.Add(x => x.PaymentStatus, FieldStorage.Yes);
        Stores.Add(x => x.BusinessStatus, FieldStorage.Yes);
    }
}