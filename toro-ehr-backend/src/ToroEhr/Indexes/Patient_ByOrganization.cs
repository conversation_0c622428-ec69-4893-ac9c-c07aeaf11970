using Raven.Client.Documents.Indexes;
using ToroEhr.Domain;

namespace ToroEhr.Indexes;

public class Patient_ByOrganization : AbstractIndexCreationTask<OrganizationPatient, Patient_ByOrganization.Entry>
{
    public class Entry
    {
        public string PatientId { get; set; } = null!;

        public string OrganizationId { get; set; } = null!;

        public string FirstName { get; set; } = null!;

        public string LastName { get; set; } = null!;

        public string Email { get; set; } = null!;

        public string Mrn { get; set; } = null!;

        public string[] SearchParams { get; set; } = null!;
    }

    public Patient_ByOrganization()
    {
        Map = orgPatients =>
            from orgPatient in orgPatients
            let patient = LoadDocument<Patient>(orgPatient.PatientId)
            let organization = LoadDocument<Organization>(orgPatient.OrganizationId)
            select new Entry
            {
                PatientId = patient.Id,
                OrganizationId = organization.Id,
                FirstName = patient.FirstName,
                LastName = patient.LastName,
                Email = patient.Email,
                Mrn = patient.Mrn,
                SearchParams = new[]
                {
                    patient.Email,
                    patient.FirstName,
                    patient.LastName,
                    patient.Mrn ?? string.Empty,
                }
            };


        Stores.Add(x => x.PatientId, FieldStorage.Yes);
        Stores.Add(x => x.OrganizationId, FieldStorage.Yes);
        Stores.Add(x => x.FirstName, FieldStorage.Yes);
        Stores.Add(x => x.LastName, FieldStorage.Yes);
        Stores.Add(x => x.Email, FieldStorage.Yes);
        Stores.Add(x => x.Mrn, FieldStorage.Yes);
        Analyzers.Add(n => n.SearchParams, "LowerCaseWhitespaceAnalyzer");
    }
}
