namespace ToroEhr.Infrastructure;

public static class Config
{
    public static class Db
    {
        public static readonly string[] Urls = DotNetEnv.Env.GetString("DATABASE_URLS").Split(';');
        public static readonly string Cert = DotNetEnv.Env.GetString("DATABASE_CERT");
        public static readonly string Name = DotNetEnv.Env.GetString("DATABASE_NAME");
    }
    
    public static class Application
    {
        public static readonly string Url = DotNetEnv.Env.GetString("APP_URL").TrimEnd('/');
        public static readonly string ApiUrl = DotNetEnv.Env.GetString("API_URL").TrimEnd('/');
        public static readonly string Name = DotNetEnv.Env.GetString("APP_NAME");
    }

    public static class S3
    {
        public static readonly string AccessKey = DotNetEnv.Env.GetString("S3_ACCESS_KEY_ID");
        public static readonly string SecretKey = DotNetEnv.Env.GetString("S3_SECRET_ACCESS_KEY");
        public static readonly string AppFilesBucketName = DotNetEnv.Env.GetString("APP_FILES_S3_BUCKET_NAME");
    }

    public static class SendGrid
    {
        public static readonly string ApiKey = DotNetEnv.Env.GetString("SENDGRID_API_KEY");
        public static readonly string InboundSubdomain = DotNetEnv.Env.GetString("INBOUND_SUBDOMAIN");
    }

    public static class Umls
    {
        public static readonly string UmlsBaseUrl = DotNetEnv.Env.GetString("UMLS_BASE_URL");
        public static readonly string UmlsApiKey = DotNetEnv.Env.GetString("UMLS_API_KEY");
    }

    public static class Ama
    {
        public static readonly string AmaBaseUrl = DotNetEnv.Env.GetString("AMA_BASE_URI");
        public static readonly string AmaClientId = DotNetEnv.Env.GetString("AMA_CLIENT_ID");
        public static readonly string AmaClientSecret = DotNetEnv.Env.GetString("AMA_CLIENT_SECRET");
    }

    public static class Twilio
    {
        public static readonly string TwilioAccountSid = DotNetEnv.Env.GetString("TWILIO_ACCOUNT_SID");
        public static readonly string TwilioAuthToken = DotNetEnv.Env.GetString("TWILIO_AUTH_TOKEN");
        public static readonly string TwilioPhoneNumber = DotNetEnv.Env.GetString("TWILIO_PHONE_NUMBER");
    }
    
    public static class Ipos
    {
        public static string IposUrl => Environment.GetEnvironmentVariable("IPOS_URL")!;
        public static string IposCloudUrl => Environment.GetEnvironmentVariable("IPOS_CLOUD_URL")!;
    }
}