using FluentValidation;
using FluentValidation.Results;

namespace ToroEhr.Infrastructure.ErrorHandling;

public static class Validator
{
    public static T Validate<T>(T @object, AbstractValidator<T> validator)
    {
        ValidationResult result = validator.Validate(@object);
        if (result.IsValid) return @object;
        throw new Exceptions.ValidationException(
            result.Errors.Select(x => new AppError(x.PropertyName, x.ErrorMessage)));
    }
}