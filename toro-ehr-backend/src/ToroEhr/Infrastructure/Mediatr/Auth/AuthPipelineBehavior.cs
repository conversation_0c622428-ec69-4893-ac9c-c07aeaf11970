using MediatR;

namespace ToroEhr.Infrastructure.Mediatr.Auth;

internal class AuthPipelineBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : AuthRequest<TResponse>
{
    public AuthPipelineBehavior(Authenticator authenticator, IEnumerable<IAuth<TRequest, TResponse>> authorizers)
    {
        _ = authenticator.User;
    }

    public Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next,
        CancellationToken cancellationToken)
    {
        return next();
    }
}