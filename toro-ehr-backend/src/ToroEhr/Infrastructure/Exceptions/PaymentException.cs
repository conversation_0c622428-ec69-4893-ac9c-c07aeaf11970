using ToroEhr.Infrastructure.ErrorHandling;

namespace ToroEhr.Infrastructure.Exceptions;

public class PaymentException : Exception
{
    public PaymentException(string message) : this(message, [])
    {
    }

    public PaymentException(string message, IEnumerable<AppError> errors) : base(message)
    {
        Errors = errors;
    }

    public PaymentException(IEnumerable<AppError> errors)
    {
        Errors = errors;
    }

    public IEnumerable<AppError> Errors { get; private set; }
}