using Coravel.Scheduling.Schedule.Interfaces;

namespace ToroEhr.Infrastructure.Exceptions;

public static class SchedulerExtensions
{
    public static IScheduledEventConfiguration EveryFirstMondayInMonth(this IScheduleInterval interval)
    {
        return interval
            .Cron("0 22 * * 1") // Runs every Monday at 22:00 UTC
            .Zoned(TimeZoneInfo.Utc)
            .When(() =>
            {
                DateTime utcNow = DateTime.UtcNow;
                // Check if today is Monday and it's within the first 7 days of the month
                return Task.FromResult(utcNow.DayOfWeek == DayOfWeek.Monday && utcNow.Day <= 7);
            });
    }
}
