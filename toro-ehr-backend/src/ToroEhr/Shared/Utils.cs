using System.Security.Cryptography;

namespace ToroEhr.Shared;

public static class Utils
{
    public static string GenerateAccessToken(int size = 64)
    {
        var tokenData = new byte[size];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenData);
        }
        return Convert.ToBase64String(tokenData);
    }
    
    public static string GenerateRefreshToken(int size = 64)
    {
        var tokenData = new byte[size];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(tokenData);
        }
        return Convert.ToBase64String(tokenData);
    }

    public static string GenerateRandomId()
    {
        return Ulid.NewUlid().ToString();
    }
    
    public static string? GenerateS3PublicFileUrl(string bucketName, string? key)
    {
        // TODO: this is temp solutions https://github.com/torohealth/toro-ehr/issues/65
        return key == null ? null :
            $"https://pub-fe984b710fb84dc69650a7f1805b3f48.r2.dev/{key}";
    }
}