namespace ToroEhr.Shared;

public record PagedSearchParams
{
    public int PageNumber { get; private set; }
    public int PageSize { get; private set; }
    public string? SearchParam { get; private set; }

    public PagedSearchParams(int? pageNumber = null, int? pageSize = null, string? searchParam = null)
    {
        PageNumber = pageNumber is null || pageNumber < 1 ? 1 : pageNumber.Value;
        PageSize = pageSize is null || pageSize <= 0 ? 10 : pageSize.Value;
        SearchParam = searchParam;
    }
}