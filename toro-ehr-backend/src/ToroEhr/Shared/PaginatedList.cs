using System.Text.Json.Serialization;

namespace ToroEhr.Shared;

public sealed class PaginatedList<T>
{
    [JsonConstructor]
    private PaginatedList(IEnumerable<T> items, int pageNumber, int pageSize, long totalItems)
    {
        Items = items;
        PageNumber = pageNumber;
        PageSize = pageSize;
        TotalItems = totalItems;
    }

    public IEnumerable<T> Items { get; private set; }

    public int PageNumber { get; private set; }

    public int PageSize { get; private set; }

    public long TotalItems { get; private set; }

    public int TotalPages => (int)Math.Ceiling(TotalItems / (double)PageSize);
    
    public static PaginatedList<T> Create(IEnumerable<T> items, long totalItemsCount, int pageNumber, int pageSize)
    {
        return new PaginatedList<T>(items, pageNumber, pageSize, totalItemsCount);
    }
}