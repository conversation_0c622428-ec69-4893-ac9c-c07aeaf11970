using FluentValidation;

namespace ToroEhr.Shared;

public record PersonRequest(
    string FirstName,
    string LastName,
    DateOnly Birthday,
    string BirthSex,
    string Phone,
    string Address);
    
public class PersonRequestValidator : AbstractValidator<PersonRequest>
{
    public PersonRequestValidator()
    {
        RuleFor(x => x.FirstName).NotEmpty();
        RuleFor(x => x.LastName).NotEmpty();
        RuleFor(x => x.Birthday).NotEmpty();
        RuleFor(x => x.BirthSex).NotEmpty();
        RuleFor(x => x.Phone).NotEmpty();
        RuleFor(x => x.Address).NotEmpty();
    }
}