using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;
using ToroEhr.Features.SnomedCode;

namespace ToroEhr.Controllers;


[ApiController]
[Route("snomed")]
[Produces("application/json")]
public class SnomedCodeController : ControllerBase
{
    private readonly IMediator _mediator;

    public SnomedCodeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List snomed codes
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<CodingResponse>>> ListSnomedCodes([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new GetSnomedCodeListQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Import snomed codes.
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(400_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportSnomedCodesCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}