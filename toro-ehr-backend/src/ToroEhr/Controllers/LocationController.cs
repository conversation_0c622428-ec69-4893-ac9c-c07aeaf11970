using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Location;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("locations")]
[Produces("application/json")]
public class LocationController : ControllerBase
{
    private readonly IMediator _mediator;

    public LocationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List locations
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<LocationResponse>>> ListLocations([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListLocationsQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     List employees lookup
    /// </summary>
    /// <returns></returns>
    [HttpGet("lookup")]
    public async Task<ActionResult<List<SelectListItem>>> ListLocationsLookup()
    {
        var result =
            await _mediator.Send(new ListLocationsLookupQuery());
        return Ok(result);
    }

    /// <summary>
    ///     Creates a new location.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateLocationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Update existing location.
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> Edit([FromBody] EditLocationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Deactivate location.
    /// </summary>
    /// <returns></returns>
    [HttpDelete]
    public async Task<ActionResult> Deactivate([FromBody] DeactivateLocationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }


    /// <summary>
    ///     Get locations with doctors 
    /// </summary>
    /// <returns></returns>
    [HttpGet("locations-with-doctors")]
    public async Task<ActionResult<List<LocationWithPractitionersResponse>>> GetLocationsWithDoctors()
    {
        var result = await _mediator.Send(new GetLocationsWithPractitionersQuery());
        return Ok(result);
    }
}
