using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Shared;
using ToroEhr.Features.OrderBundlesTemplate;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.Encounter;

namespace ToroEhr.Controllers;

[ApiController]
[Route("bundle-templates")]
[Produces("application/json")]
public class OrderBundleTemplateController : ControllerBase
{
    private readonly IMediator _mediator;

    public OrderBundleTemplateController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List Order Bundle Template
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<OrderBundleTemplateListResponse>>> GetOrderBundleTemplateList(
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(
                new GetOrderBundleTemplateListQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Get order bundle template
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<OrderBundleTemplateResponse>> GetOrderBundleTemplate([FromRoute] string id)
    {
        var result = await _mediator.Send(new GetOrderBundleTemplateQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Create Order Bundle Template
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<string>> CreateOrderBundleTemplate(
        [FromBody] CreateOrderBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }


    /// <summary>
    ///     Edit Order Bundle Template
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> EditOrderBundleTemplate([FromBody] EditOrderBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Add Lab Order Entry to bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPost("lab-order-entry")]
    public async Task<ActionResult<string>> AddLabOrderEntryToBundleTemplate(
        [FromBody] AddLabOrderEntryToBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit Lab Order Entry in bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPut("lab-order-entry")]
    public async Task<ActionResult<string>> EditLabOrderEntryInBundleTemplate(
        [FromBody] EditLabOrderEntryInBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Add Procedure Order Entry to bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPost("procedure-order-entry")]
    public async Task<ActionResult<string>> AddProcedureOrderEntryToBundleTemplate(
        [FromBody] AddProcedureOrderEntryToBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit Procedure Order Entry in bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPut("procedure-order-entry")]
    public async Task<ActionResult<string>> EditProcedureOrderEntryInBundleTemplate(
        [FromBody] EditProcedureOrderEntryInBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Add Medication Order Entry to bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPost("medication-order-entry")]
    public async Task<ActionResult<string>> AddMedicationOrderEntryToBundleTemplate(
        [FromBody] AddMedicationOrderEntryToBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Edit Medication Order Entry in bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPut("medication-order-entry")]
    public async Task<ActionResult<string>> EditMedicationOrderEntryInBundleTemplate(
        [FromBody] EditMedicationOrderEntryInBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Add Bundle Order Entry to bundle template
    /// </summary>
    /// <returns></returns>
    [HttpPost("bundle-order-entry")]
    public async Task<ActionResult<string>> AddBundleOrderEntryToBundleTemplate(
        [FromBody] AddBundleOrderEntryToBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Delete Order Entry in bundle template
    /// </summary>
    /// <returns></returns>
    [HttpDelete("order-entry")]
    public async Task<ActionResult<string>> DeleteOrderEntryFromBundleTemplate(
        [FromBody] DeleteOrderEntryFromBundleTemplateCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Add Order Bundle Template Step
    /// </summary>
    /// <returns></returns>
    [HttpPost("steps")]
    public async Task<ActionResult<string>> AddOrderBundleTemplateStep(
        [FromBody] AddOrderBundleTemplateStepCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Delete Order Bundle Template Step
    /// </summary>
    /// <returns></returns>
    [HttpDelete("steps")]
    public async Task<ActionResult<string>> DeleteOrderBundleTemplateStep(
        [FromBody] DeleteOrderBundleTemplateStepCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Search orders medicine, labs adn procedures
    /// </summary>
    /// <returns></returns>
    [HttpGet("order-entries")]
    public async Task<ActionResult<PaginatedList<SearchOrderEntryResponse>>> SearchOrderEntry(
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new SearchOrderEntryQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }
}