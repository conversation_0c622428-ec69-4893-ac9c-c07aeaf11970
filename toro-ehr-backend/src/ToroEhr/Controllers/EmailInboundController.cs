using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Encounter;

namespace ToroEhr.Controllers;

[ApiController]
[Route("email/inbound")]
[Produces("application/json")]
public class EmailInboundController : ControllerBase
{
    private readonly IMediator _mediator;

    public EmailInboundController(IMediator mediator)
    {
        _mediator = mediator;
    }
    [HttpPost]
    public async Task<IActionResult> ReceiveEmail()
    {
        var form = await Request.ReadFormAsync();

        var from = form["from"].ToString();
        var to = form["to"].ToString();
        var subject = form["subject"].ToString();
        var text = form["text"].ToString();

        var result =
            await _mediator.Send(new ReceiveEncounterEmailCommand(to, subject, text, form.Files));
        return Ok(result);
    }
}