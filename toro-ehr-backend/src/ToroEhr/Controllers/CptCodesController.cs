using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Shared;
using ToroEhr.Features.CptCodes;

namespace ToroEhr.Controllers;

[ApiController]
[Route("cptcodes")]
[Produces("application/json")]
public class CptCodesController : ControllerBase
{
    private readonly IMediator _mediator;

    public CptCodesController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List CPT codes
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<CptCodeResponse>>> ListCptCodes([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListCptCodesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Import CPT codes
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(200_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportCptCodesCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}
