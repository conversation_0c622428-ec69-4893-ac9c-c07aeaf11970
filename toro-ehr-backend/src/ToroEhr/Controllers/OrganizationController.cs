using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Organization;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("organizations")]
[Produces("application/json")]
public class OrganizationController : ControllerBase
{
    private readonly IMediator _mediator;

    public OrganizationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List organizations
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<OrganizationResponse>>> ListOrganizations([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result = await _mediator.Send(new ListOrganizationsQuery(
            new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Creates a new organization.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<string>> Create([FromBody] CreateOrganizationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Update existing organization.
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> Edit([FromBody] EditOrganizationCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}