using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Encounter;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.NoteTemplate;
using ToroEhr.Shared;

namespace ToroEhr.Controllers;

[ApiController]
[Route("notes")]
[Produces("application/json")]
public class NoteController : ControllerBase
{
    private readonly IMediator _mediator;
    
    public NoteController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List patient notes
    /// </summary>
    /// <returns></returns>
   [HttpGet("patientId")]
    public async Task<ActionResult<List<PatientNoteResponse>>> ListPatientNotes([FromQuery] string patientId)
    {
        var result = await _mediator.Send(new ListPatientNotesQuery(patientId));
        return Ok(result);
    }
    
    /// <summary>
    ///     List patient notes versions
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}/versions")]
    public async Task<ActionResult<List<PatientNoteResponse>>> ListPatientNotesVersions(string id)
    {
        var result = await _mediator.Send(new ListPatientNotesVersionsQuery(id));
        return Ok(result);
    }
    
    /// <summary>
    ///     Create note
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<string>> CreateNote([FromBody] CreateNoteCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
    
    /// <summary>
    ///     Amend note
    /// </summary>
    /// <returns></returns>
    [HttpPut]
    public async Task<ActionResult<string>> AmendNote([FromBody] AmendNoteCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}