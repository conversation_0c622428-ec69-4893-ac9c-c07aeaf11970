using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;
using ToroEhr.Features.Icd10;

namespace ToroEhr.Controllers;

[ApiController]
[Route("icd10")]
[Produces("application/json")]
public class Icd10Controller : ControllerBase
{
    private readonly IMediator _mediator;

    public Icd10Controller(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List ICD 10 codes
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<CodingResponse>>> ListIcd10([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListIcd10Query(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Import ICD 10 codes
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(200_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportIcd10Command command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}
