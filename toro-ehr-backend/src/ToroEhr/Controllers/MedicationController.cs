using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Medication;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Controllers;


[ApiController]
[Route("medications")]
[Produces("application/json")]
public class MedicationController : ControllerBase
{
    private readonly IMediator _mediator;

    public MedicationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List medications
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<CodingResponse>>> ListMedications([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListMedicationsQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Import medications.
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(300_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportMedicationsCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    /// <summary>
    ///     Get Medications By Ingredient
    /// </summary>
    /// <returns></returns>
    [HttpGet("ingredient/{ingredientId}")]
    public async Task<ActionResult<List<SelectListItem>>> GetMedicationsByIngredient([FromRoute]string ingredientId)
    {
        var result =
            await _mediator.Send(new GetMedicationsByIngredientQuery(ingredientId));
        return Ok(result);
    }
}
