using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Allergie;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Controllers;

[ApiController]
[Route("allergies")]
[Produces("application/json")]
public class AllergieController : ControllerBase
{
    private readonly IMediator _mediator;

    public AllergieController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List allergies
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<CodingResponse>>> ListAllergiess([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListAllergiesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Import allergies.
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(200_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportAllergiesCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}
