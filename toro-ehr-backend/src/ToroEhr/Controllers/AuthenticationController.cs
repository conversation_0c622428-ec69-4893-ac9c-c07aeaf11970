using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Features.Authentication;
using ToroEhr.Features.Employee;

namespace ToroEhr.Controllers
{
    [ApiController]
    [Route("authentication")]
    public class AuthenticationController : ControllerBase
    {
        private readonly IMediator _mediator;

        public AuthenticationController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        [Route("login")]
        public async Task<ActionResult<string>> LoginUser(LoginUserCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
        
        [HttpGet]
        [Route("user")]
        public async Task<ActionResult<UserInfoResponse>> LoginUser()
        {
            var result = await _mediator.Send(new GetUserInfoQuery());
            return Ok(result);
        }
        
        [HttpPost]
        [Route("set-password-employee")]
        public async Task<ActionResult<string>> SetPasswordEmployee(SetPasswordEmployeeCommand command)
        {
            string result = await _mediator.Send(command);
            return Ok(result);
        }
        
        [HttpPost]
        [Route("set-password-patient")]
        public async Task<ActionResult<string>> SetPasswordPatient(SetPasswordPatientCommand command)
        {
            string result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPost]
        [Route("seed")]
        public async Task<ActionResult<string>> Seed()
        {
            string result = await _mediator.Send(new SeedCommand());
            return Ok(result);
        }

        [HttpPost]
        [Route("update-session")]
        public async Task<ActionResult<Unit>> UpdateSession(UpdateSessionCommand command)
        {
            var result = await _mediator.Send(command);
            return Ok(result);
        }
    }
}