using MediatR;
using Microsoft.AspNetCore.Mvc;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;
using ToroEhr.Features.LoincCode;

namespace ToroEhr.Controllers;

[ApiController]
[Route("loinc")]
[Produces("application/json")]
public class LoincCodeController : ControllerBase
{
    private readonly IMediator _mediator;

    public LoincCodeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    ///     List Loinc codes
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<PaginatedList<CodingResponse>>> ListLoincCodes([FromQuery] int? pageNumber,
        [FromQuery] int? pageSize, [FromQuery] string? searchParam)
    {
        var result =
            await _mediator.Send(new ListLoincCodesQuery(new PagedSearchParams(pageNumber, pageSize, searchParam)));
        return Ok(result);
    }

    /// <summary>
    ///     Get Loinc code by id
    /// </summary>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<LoincCodeDetailsResponse>> GetLoincCodeById([FromRoute] string id)
    {
        var result =
            await _mediator.Send(new GetLoincCodeByIdQuery(id));
        return Ok(result);
    }

    /// <summary>
    ///     Import Loinc codes
    /// </summary>
    /// <returns></returns>
    [RequestSizeLimit(200_000_000)]
    [HttpPost]
    public async Task<ActionResult<string>> Import([FromForm] ImportLoincCodesCommand command)
    {
        var result = await _mediator.Send(command);
        return Ok(result);
    }
}