using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Infrastructure.Exceptions;

namespace ToroEhr.Features.Location;

public sealed record DeactivateLocationCommand(string Id) : AuthRequest<Unit>;

internal sealed class DeactivateLocationAuth : IAuth<DeactivateLocationCommand, Unit>
{
    public DeactivateLocationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin]);
    }
}

internal sealed class DeactivateLocationHandler : IRequestHandler<DeactivateLocationCommand, Unit>
{
    private readonly IDocumentStore _store;

    public DeactivateLocationHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(DeactivateLocationCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Location location = await session.LoadAsync<Domain.Location>(request.Id) ??
           throw new NotFoundException($"Location with 'id:' {request.Id} not found!");

        location.Deactivate(DateTimeOffset.UtcNow);

        await session.StoreAsync(location, cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
