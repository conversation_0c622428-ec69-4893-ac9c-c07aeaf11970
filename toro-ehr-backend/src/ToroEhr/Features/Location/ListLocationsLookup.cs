using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Location;

public sealed record ListLocationsLookupQuery : AuthRequest<List<SelectListItem>>;

internal sealed class ListLocationsLookupAuth : IAuth<ListLocationsLookupQuery, List<SelectListItem>>
{
    public ListLocationsLookupAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListLocationsLookupHandler : IRequestHandler<ListLocationsLookupQuery, List<SelectListItem>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListLocationsLookupHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<SelectListItem>> Handle(ListLocationsLookupQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        var locations = await session
            .Query<Domain.Location>()
            .Where(x => x.OrganizationId == _user.SelectedOrganizationId && x.DeactivatedAt == null)
            .ToListAsync(cancellationToken);

        var items = new List<SelectListItem>();
        foreach (var location in locations)
        {
            items.Add(new SelectListItem(location.Name, location.Id));
        }

        return items;
    }
}