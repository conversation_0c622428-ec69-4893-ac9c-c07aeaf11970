using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;
using ToroEhr.Enums;
using Raven.Client.Documents.Linq;

namespace ToroEhr.Features.Location;

public sealed record GetLocationsWithPractitionersQuery()
    : AuthRequest<List<LocationWithPractitionersResponse>>;

public sealed record LocationWithPractitionersResponse(string LocationId, string LocationName, string OrganizationName, 
    List<PractitionerResponse> Practitioners);
public sealed record PractitionerResponse(string EmployeeId, string FullName);

internal sealed class LocationWithPractitionersAuth : IAuth<GetLocationsWithPractitionersQuery, List<LocationWithPractitionersResponse>>
{
    public LocationWithPractitionersAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    LocationWithPractitionersHandler : IRequestHandler<GetLocationsWithPractitionersQuery, List<LocationWithPractitionersResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public LocationWithPractitionersHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<LocationWithPractitionersResponse>> Handle(GetLocationsWithPractitionersQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        
        List<string> organizationPatientIds = await session.Query<OrganizationPatient>()
            .Where(x => x.PatientId == _user.PatientId)
            .Select(x => x.OrganizationId)
            .ToListAsync(cancellationToken);
        
        List<Domain.Location> locations = await session.Query<Domain.Location>()
            .Where(x => x.OrganizationId.In(organizationPatientIds))
            .Include(x => x.OrganizationId)
            .ToListAsync(cancellationToken);

        List<LocationEmployee> locationEmployees = await session.Query<LocationEmployee>()
        .Include(x => x.EmployeeId)
            .Where(x => x.LocationId.In(locations.Select(x => x.Id).ToList()) && x.EmployeeRoles.Contains(EmployeeRole.Practitioner.Name))
            .ToListAsync(cancellationToken);

        var result = new List<LocationWithPractitionersResponse>();

        foreach (var location in locations)
        {
            Domain.Organization organization = await session.LoadAsync<Domain.Organization>(location.OrganizationId);
            List<string> practitionerIds = locationEmployees
                .Where(x => x.LocationId == location.Id)
                .Select(x => x.EmployeeId).ToList();
            var practitioners = await session.LoadAsync<Domain.Employee>(practitionerIds);

            result.Add(new LocationWithPractitionersResponse(location.Id, location.Name, organization.Name,
                practitioners.Values.Select(x => new PractitionerResponse(x.Id, x.FullName)).ToList()));
        }

        return result;
    }
}