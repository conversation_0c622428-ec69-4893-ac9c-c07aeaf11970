using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;
using ToroEhr.Infrastructure.Exceptions;

namespace ToroEhr.Features.Location;

public sealed record EditLocationCommand(
    string Id,
    string Name,
    string Classification,
    bool IsDefault,
    string PhoneNumber,
    string TaxIdentificationNumber,
    int MarkMissedTime,
    decimal MissedFeeInCents,
    int CheckInStartOffsetHours,
    AddressRequest Address) : AuthRequest<string>;

internal sealed class EditLocationCommandValidator : AbstractValidator<EditLocationCommand>
{
    public EditLocationCommandValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.TaxIdentificationNumber).NotEmpty();
        RuleFor(x => x.Address).SetValidator(new AddressRequestValidator());
    }
}

internal sealed class EditLocationAuth : IAuth<EditLocationCommand, string>
{
    public EditLocationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin]);
    }
}

internal sealed class EditLocationHandler : IRequestHandler<EditLocationCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public EditLocationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(EditLocationCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Location location = await session.LoadAsync<Domain.Location>(request.Id, cancellationToken) ??
                                   throw new NotFoundException($"Location with 'id:' {request.Id} not found!");

        Domain.Address address = Domain.Address.Create(request.Address.Street, request.Address.City,
            request.Address.State, request.Address.ZipCode);

        location.Update(request.Name, request.Classification, request.IsDefault, request.PhoneNumber,
            request.TaxIdentificationNumber, request.MarkMissedTime, request.MissedFeeInCents,
            request.CheckInStartOffsetHours, address);

        if (request.IsDefault)
        {
            var existingDefaultLocations = await session.Query<Domain.Location>()
                .Where(x => x.OrganizationId == _user.SelectedOrganizationId && x.IsDefault && x.Id != request.Id)
                .ToListAsync(token: cancellationToken);

            foreach (var existingDefaultLocation in existingDefaultLocations)
            {
                existingDefaultLocation.UpdateDefault(false);
            }
        }

        await session.SaveChangesAsync(cancellationToken);
        return location.Id;
    }
}