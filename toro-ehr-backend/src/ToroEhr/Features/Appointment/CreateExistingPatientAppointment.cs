using HandlebarsDotNet;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment.Events;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Appointment;

public sealed record CreateExistingPatientAppointmentCommand(
    string PatientId,
    string EmployeeId,
    string LocationId,
    int DurationInMinutes,
    DateTimeOffset StartAt,
    bool InitiatedByPatient) : AuthRequest<string>
{
}

internal class CreateExistingPatientAppointmentAuth : IAuth<CreateExistingPatientAppointmentCommand, string>
{
    public CreateExistingPatientAppointmentAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    CreateExistingPatientAppointmentHandler : IRequestHandler<CreateExistingPatientAppointmentCommand, string>
{
    private readonly IDocumentStore _store;

    public CreateExistingPatientAppointmentHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(CreateExistingPatientAppointmentCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.LocationEmployee locationEmployee =
            await session.Query<Domain.LocationEmployee>()
                .Include(x => x.LocationId)
                .Include(x => x.EmployeeId)
                .Where(x => x.EmployeeId == command.EmployeeId && x.LocationId == command.LocationId)
                .FirstOrDefaultAsync(cancellationToken);

        Domain.Employee employee =
            await session.LoadAsync<Domain.Employee>(command.EmployeeId, cancellationToken);
        Domain.Location location = await session.LoadAsync<Domain.Location>(command.LocationId, cancellationToken);

        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(command.PatientId, cancellationToken);

        AppointmentStatus appointmentStatus = command.InitiatedByPatient && locationEmployee.PendingApprovalAppointments
            ? AppointmentStatus.Pending
            : AppointmentStatus.Confirmed;

        string? encounterId = null;
        if (appointmentStatus == AppointmentStatus.Confirmed)
        {
            var encounter = Domain.Encounter.Create(command.PatientId, command.EmployeeId, command.LocationId,
                location.Classification, command.StartAt);
            await session.StoreAsync(encounter, cancellationToken);
            encounterId = encounter.Id;
        }

        Domain.Appointment appointment = Domain.Appointment.Create(command.PatientId, command.EmployeeId,
            command.LocationId, encounterId, command.StartAt, command.StartAt.AddMinutes(command.DurationInMinutes),
            command.DurationInMinutes,
            appointmentStatus);

        await session.StoreAsync(appointment, cancellationToken);

        if (appointmentStatus == AppointmentStatus.Confirmed)
        {
            if (patient.PreferredContactMethod == "Text" && patient.PhoneNumbers.Any(x => x.IsPrimary))
            {
                await session.StoreAsync(
                    new SendSmsAppointmentConfirmed(patient, employee, appointment, location.FormattedAddress),
                    cancellationToken);
            }
            else
            {
                await session.StoreAsync(
                    new SendEmailAppointmentConfirmed(patient, employee, appointment, location.FormattedAddress,
                        command.DurationInMinutes), cancellationToken);
            }
        }

        await session.SaveChangesAsync(cancellationToken);

        return appointment.Id;
    }
}