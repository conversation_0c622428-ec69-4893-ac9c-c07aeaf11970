using MediatR;
using ToroEhr.Features.Shared;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment.Events;

public record SendSmsAppointmentConfirmed(Domain.Patient Patient, Domain.Employee Employee, 
    Domain.Appointment Appointment, string LocationAddress) : BaseEventEntity;

public class SendSmsAppointmentConfirmedEventHandler : INotificationHandler<SendEmailAppointmentConfirmed>
{
    private readonly SmsService _smsService;

    public SendSmsAppointmentConfirmedEventHandler(SmsService smsService)
    {
        _smsService = smsService;
    }
    public async Task Handle(SendEmailAppointmentConfirmed notification, CancellationToken cancellationToken = new())
    {
        await _smsService.SendSmsAsync(notification.Patient.PhoneNumbers.First(x => x.IsPrimary).Number,
        $"Your appointment with {notification.Employee.FullName} is confirmed. \n Appointment Details \n Location: {notification.LocationAddress} \n Date: {notification.Appointment.StartAt.ToString("f")}");

    }
}