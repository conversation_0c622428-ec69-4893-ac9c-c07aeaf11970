using HandlebarsDotNet;
using MediatR;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Services;
using ToroEhr.Templates.Email.AppointmentConfirmed;

namespace ToroEhr.Features.Appointment.Events;


public record SendEmailAppointmentConfirmed(Domain.Patient Patient, Domain.Employee Employee, 
    Domain.Appointment Appointment, string LocationAddress, int DurationInMinutes) : BaseEventEntity;

public class SendEmailAppointmentConfirmedEventHandler : INotificationHandler<SendEmailAppointmentConfirmed>
{
    private readonly EmailService _emailService;

    public SendEmailAppointmentConfirmedEventHandler(EmailService emailService)
    {
        _emailService = emailService;
    }
    public async Task Handle(SendEmailAppointmentConfirmed notification, CancellationToken cancellationToken = new())
    {
        var rootDir = Path.Combine("Templates", "Email", "AppointmentConfirmed");
        var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        var compiledTemplate = Handlebars.Compile(htmlTemplate);

        string email = compiledTemplate(new AppointmentConfirmedEmail(Config.Application.Name, Config.Application.Url,
        $"{notification.Patient.FirstName} {notification.Patient.LastName}", $"{notification.Employee.FirstName} {notification.Employee.LastName}",
        notification.LocationAddress, notification.Appointment.StartAt.ToString("f"), notification.DurationInMinutes));
        string subject = "Appointment Confirmed";

        await _emailService.SendEmailAsync(notification.Patient.Email, subject, email);
    }
}
