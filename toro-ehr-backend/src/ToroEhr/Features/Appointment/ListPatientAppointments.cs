using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Appointment;

public sealed record ListPatientAppointmentsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<AppointmentResponse>>;

public sealed record AppointmentResponse(
    string Id,
    string EncounterId,
    string EmployeeName,
    string Location,
    DateTimeOffset StartAt,
    int DurationInMinutes,
    string Status,
    DateTimeOffset CheckInAvailableAt,
    int CheckInStartOffsetHours,
    decimal MissedAppointmentFeeInCents);

internal sealed class ListLocationsAuth : IAuth<ListPatientAppointmentsQuery, PaginatedList<AppointmentResponse>>
{
    public ListLocationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    ListLocationsHandler : IRequestHandler<ListPatientAppointmentsQuery, PaginatedList<AppointmentResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListLocationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<AppointmentResponse>> Handle(ListPatientAppointmentsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Appointments_ByParticipant.Entry> dbQuery = session
            .Query<Appointments_ByParticipant.Entry, Appointments_ByParticipant>()
            .OrderBy(x => x.StartAt)
            .Include(x => x.AppointmentId)
            .ProjectInto<Appointments_ByParticipant.Entry>();

        if (_user.IsPatient)
        {
            dbQuery = dbQuery.Where(x => x.PatientId == _user.PatientId);
        }

        var queryItems = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        var appointments =
            await session.LoadAsync<Domain.Appointment>(queryItems.Select(x => x.AppointmentId).ToList(),
                cancellationToken);

        IEnumerable<AppointmentResponse> resultItems = queryItems.Select(item =>
        {
            var appointment = appointments[item.AppointmentId];
            return new AppointmentResponse(appointment.Id, item.EncounterId, item.EmployeeFullName,
                item.LocationName, appointment.StartAt, appointment.DurationInMinutes, appointment.Status,
                appointment.StartAt.AddMinutes(-item.CheckInStartOffsetHours), item.CheckInStartOffsetHours,
                item.MissedAppointmentFeeInCents);
        }).ToList();

        return PaginatedList<AppointmentResponse>.Create(resultItems, stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}