using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Enums;

namespace ToroEhr.Features.Appointment;

public sealed record EditAppointmentCommand(
    string Id,
    string EmployeeId,
    string LocationId,
    DateTimeOffset StartAt,
    int DurationInMinutes) : AuthRequest<string>
{
}

internal class EditAppointmentAuth : IAuth<EditAppointmentCommand, string>
{
    public EditAppointmentAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class EditAppointmentHandler : IRequestHandler<EditAppointmentCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly EmailService _emailService;

    public EditAppointmentHandler(IDocumentStore store, EmailService emailService)
    {
        _store = store;
        _emailService = emailService;
    }

    public async Task<string> Handle(EditAppointmentCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Appointment appointment = await session.LoadAsync<Domain.Appointment>(command.Id, cancellationToken);

        Guard.AgainstNotFound(appointment, new("Appointment.NotFound", $"Appointment with 'id:' {command.Id} not found!"));

        appointment.Update(command);

        await session.StoreAsync(appointment, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        //await SendEmailAppointmentConfirmed(command, cancellationToken, patient, practitioner, appointment,
        //    location.FormattedAddress);

        return appointment.Id;
    }

    //private async Task SendEmailAppointmentConfirmed(CreateExistingPatientAppointmentCommand command,
    //    CancellationToken cancellationToken, Domain.Patient patient, Domain.Practitioner practitioner,
    //    Domain.Appointment appointment, string locationAddress)
    //{
    //    var rootDir = Path.Combine("Templates", "Email", "AppointmentConfirmed");
    //    var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
    //    var compiledTemplate = Handlebars.Compile(htmlTemplate);

    //    string email = compiledTemplate(new AppointmentConfirmedEmail(Config.Application.Name, Config.Application.Url,
    //        $"{patient.FirstName} {patient.LastName}", $"{practitioner.FirstName} {practitioner.LastName}",
    //        locationAddress,
    //        appointment.StartAt.ToString("f"), command.DurationInMinutes));
    //    string subject = "Appointment Confirmed";

    //    await _emailService.SendEmailAsync(practitioner.Email, subject, email);
    //}
}
