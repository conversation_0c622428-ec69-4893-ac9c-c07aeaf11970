using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Appointment;

public sealed record MarkAppointmentAsCheckedInCommand(string Id) : AuthRequest<string>;

internal class MarkAppointmentAsCheckedInAuth : IAuth<MarkAppointmentAsCheckedInCommand, string>
{
    public MarkAppointmentAsCheckedInAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal class MarkAppointmentAsCheckedInHandler : IRequestHandler<MarkAppointmentAsCheckedInCommand, string>
{
    private readonly IDocumentStore _store;

    public MarkAppointmentAsCheckedInHandler(IDocumentStore store, EmailService emailService)
    {
        _store = store;
    }

    public async Task<string> Handle(MarkAppointmentAsCheckedInCommand command,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Appointment appointment = await session.LoadAsync<Domain.Appointment>(command.Id, cancellationToken);

        Guard.AgainstNotFound(appointment,
            new("Appointment.NotFound", $"Appointment with 'id:' {command.Id} not found!"));

        appointment.CheckIn();
        await session.SaveChangesAsync(cancellationToken);

        return appointment.Id;
    }
}