using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment.Events;
using ToroEhr.Features.Appointment.Shared;
using ToroEhr.Features.Authentication.Events;
using ToroEhr.Infrastructure.Exceptions;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Appointment;

public sealed record CreateNewPatientAppointmentCommand(
    string FirstName,
    string LastName,
    DateTime Birthday,
    string Email,
    string PhoneNumber,
    string EmployeeId,
    string LocationId,
    int DurationInMinutes,
    DateTimeOffset StartAt) : AuthRequest<string>;

internal class CreateNewPatientAppointmentAuth : IAuth<CreateNewPatientAppointmentCommand, string>
{
    public CreateNewPatientAppointmentAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class CreateNewPatientAppointmentHandler : IRequestHandler<CreateNewPatientAppointmentCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateNewPatientAppointmentHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateNewPatientAppointmentCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Patient patient = await session.Query<Domain.Patient>()
            .Where(x => x.FirstName == command.FirstName && x.LastName == command.LastName &&
                        x.Birthday == command.Birthday).FirstOrDefaultAsync(cancellationToken);

        if (patient == null)
        {
            // Generate unique MRN first
            var mrn = await MrnService.GenerateUniqueMrnAsync(session, cancellationToken);

            patient = Domain.Patient.Create(mrn, command.Email, command.FirstName, command.LastName, command.Birthday,
                command.PhoneNumber);

            OrganizationPatient organizationPatient = OrganizationPatient.Create(_user.SelectedOrganizationId!,
                patient.Id, Utils.GenerateRandomId(), command.Timestamp);

            await session.StoreAsync(patient, cancellationToken);
            await session.StoreAsync(organizationPatient, cancellationToken);

            var user = await session
                .Query<User>()
                .Include(x => x.PatientId)
                .FirstOrDefaultAsync(x => x.PrimaryEmail == command.Email, token: cancellationToken);

            if (user == null)
            {
                user = User.Create(command.Email, command.FirstName, command.LastName, patient.Id, null,
                    [UserRole.Patient.Name]);
                await session.StoreAsync(user, cancellationToken);
                await session.StoreAsync(new SendInvitationEmail(patient.FirstName, patient.LastName, patient.Email,
                    _user, organizationPatient.InvitationToken!), cancellationToken);
                await session.StoreAsync(new SendInvitationSms(patient, _user, organizationPatient.InvitationToken!),
                    cancellationToken);
            }
            else
            {
                user.SetPatient(patient.Id);
            }
        }
        else
        {
            IEnumerable<OrganizationPatient> patientOrgs = await session
                .Query<OrganizationPatient>()
                .Where(x => x.PatientId == patient.Id).ToListAsync(token: cancellationToken);

            if (patient.Email != command.Email)
            {
                // TODO: Cover this
            }
            else
            {
                if (patientOrgs.Any(x => x.OrganizationId == _user.SelectedOrganizationId))
                {
                    throw new ValidationException([AppointmentErrors.AlreadyExists(command.Email)]);
                }

                OrganizationPatient organizationPatient = OrganizationPatient.Create(_user.SelectedOrganizationId!,
                    patient.Id, Utils.GenerateRandomId(), command.Timestamp);
                await session.StoreAsync(organizationPatient, cancellationToken);
            }
        }

        Domain.Employee employee =
            await session.LoadAsync<Domain.Employee>(command.EmployeeId, cancellationToken);

        Domain.Location location = await session.LoadAsync<Domain.Location>(command.LocationId, cancellationToken);

        var encounter = Domain.Encounter.Create(patient.Id, command.EmployeeId, command.LocationId,
            location.Classification, command.StartAt);
        await session.StoreAsync(encounter, cancellationToken);

        Domain.Appointment appointment = Domain.Appointment.Create(patient.Id, command.EmployeeId,
            command.LocationId, encounter.Id, command.StartAt,
            command.StartAt.AddMinutes(command.DurationInMinutes),
            command.DurationInMinutes, AppointmentStatus.Confirmed);

        await session.StoreAsync(appointment, cancellationToken);

        await session.StoreAsync(new SendEmailAppointmentConfirmed(patient, employee, appointment,
            location.FormattedAddress,
            command.DurationInMinutes), cancellationToken);

        await session.StoreAsync(
            new SendSmsAppointmentConfirmed(patient, employee, appointment, location.FormattedAddress),
            cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return appointment.Id;
    }
}