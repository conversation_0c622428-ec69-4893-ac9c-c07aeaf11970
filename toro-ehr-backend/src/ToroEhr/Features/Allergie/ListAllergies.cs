using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Allergie;

public sealed record ListAllergiesQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CodingResponse>>;

internal sealed class ListAllergiesAuth : IAuth<ListAllergiesQuery, PaginatedList<CodingResponse>>
{
    public ListAllergiesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListAllergiesHandler : IRequestHandler<ListAllergiesQuery, PaginatedList<CodingResponse>>
{
    private readonly IDocumentStore _store;
    public ListAllergiesHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CodingResponse>> Handle(ListAllergiesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.Allergie> dbQuery = session.Query<Domain.Allergie>();

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.Code, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.DisplayName, $"{query.PagedSearchParams.SearchParam}*");
        }

        var allergies = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<CodingResponse>.Create(
            allergies.Select(x => new CodingResponse(x.Id, x.Code, x.CodeSystem, x.CodeSystemName, 
            x.CodeSystemVersion, x.DisplayName)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}