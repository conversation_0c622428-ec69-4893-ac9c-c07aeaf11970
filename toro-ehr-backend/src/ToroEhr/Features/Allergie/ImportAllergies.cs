using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Allergie;

public sealed record ImportAllergiesCommand(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportAllergiesCommandValidator : AbstractValidator<ImportAllergiesCommand>
{
    public ImportAllergiesCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportAllergiesAuth : IAuth<ImportAllergiesCommand, Unit>
{
    public ImportAllergiesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportAllergiesHandler : IRequestHandler<ImportAllergiesCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportAllergiesHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportAllergiesCommand request, CancellationToken cancellationToken)
    {
        var allergies = await CodeService.ParseCodingExcelFile(request.File);

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        foreach (var allergiesRow in allergies)
        {
            var allergie = Domain.Allergie.Create(allergiesRow.Code, allergiesRow.CodeSystem, allergiesRow.CodeSystemName,
                allergiesRow.CodeSystemVersion, allergiesRow.DisplayName);
            await session.StoreAsync(allergie, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}