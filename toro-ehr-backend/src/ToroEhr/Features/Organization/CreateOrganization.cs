using FluentValidation;
using HandlebarsDotNet;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;
using ToroEhr.Templates.Email.SetPassword;

namespace ToroEhr.Features.Organization;

public record CreateOrganizationCommand(
    string OrganizationName,
    string LocationName,
    OrganizationAdminRequest OrganizationAdmin
) : AuthRequest<string>;

public record OrganizationAdminRequest(string FirstName, string LastName, string Email);

internal class CreateOrganizationAuth : IAuth<CreateOrganizationCommand, string>
{
    public CreateOrganizationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public class CreateOrganizationCommandValidator : AbstractValidator<CreateOrganizationCommand>
{
    public CreateOrganizationCommandValidator()
    {
        RuleFor(x => x.OrganizationName).NotEmpty();
        RuleFor(x => x.LocationName).NotEmpty();
        RuleFor(x => x.OrganizationAdmin).NotEmpty()
            .ChildRules(x =>
            {
                x.RuleFor(oa => oa.FirstName).NotEmpty();
                x.RuleFor(oa => oa.LastName).NotEmpty();
                x.RuleFor(oa => oa.Email).EmailAddress().NotEmpty();
            });
    }
}

internal class CreateOrganizationHandler : IRequestHandler<CreateOrganizationCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly EmailService _emailService;

    public CreateOrganizationHandler(IDocumentStore store, EmailService emailService)
    {
        _store = store;
        _emailService = emailService;
    }

    public async Task<string> Handle(CreateOrganizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Organization organization = Domain.Organization.Create(command.OrganizationName);
        // default classification is ambulatory
        Domain.Location location = Domain.Location.Create(organization.Id, command.LocationName, "Ambulatory", true, string.Empty,
            string.Empty, 0, 0, 48, null, null);

        Domain.Employee organizationAdmin = Domain.Employee.Create(command.OrganizationAdmin.Email,
            command.OrganizationAdmin.FirstName,
            command.OrganizationAdmin.LastName, string.Empty, string.Empty, null);
        LocationEmployee locationEmployee = LocationEmployee.Create(organization.Id, location.Id, organizationAdmin.Id,
            [EmployeeRole.OrganizationAdmin.Name], "#800080", string.Empty, Utils.GenerateRandomId(),
            command.Timestamp, false);

        User existingUser = await session.Query<User>()
            .FirstOrDefaultAsync(x => x.PrimaryEmail == command.OrganizationAdmin.Email, token: cancellationToken);

        if (existingUser == null)
        {
            User user =
                User.Create(command.OrganizationAdmin.Email, command.OrganizationAdmin.FirstName,
                    command.OrganizationAdmin.LastName, null, organizationAdmin.Id,
                    [UserRole.Employee.Name]);
            await session.StoreAsync(user, cancellationToken);
        }

        await session.StoreAsync(organization, cancellationToken);
        await session.StoreAsync(location, cancellationToken);
        await session.StoreAsync(organizationAdmin, cancellationToken);
        await session.StoreAsync(locationEmployee, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        var rootDir = Path.Combine("Templates", "Email", "SetPassword");
        var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        var compiledTemplate = Handlebars.Compile(htmlTemplate);

        string email = compiledTemplate(new SetPasswordEmail(Config.Application.Name, Config.Application.Url,
            command.OrganizationAdmin.FirstName, Config.Application.Name, command.OrganizationName,
            $"{Config.Application.Url}/set-password-Employee?invitationToken={locationEmployee.InvitationToken}"));
        string subject = "Set up account";
        await _emailService.SendEmailAsync(command.OrganizationAdmin.Email, subject, email);

        return organization.Id;
    }
}