using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Organization.Shared;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Infrastructure.Exceptions;
using ToroEhr.Infrastructure.Guards;

namespace ToroEhr.Features.Organization;

public record EditOrganizationCommand(string Id, string Name) : AuthRequest<string>;

internal class EditOrganizationAuth : IAuth<EditOrganizationCommand, string>
{
    public EditOrganizationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public class EditOrganizationCommandValidator : AbstractValidator<EditOrganizationCommand>
{
    public EditOrganizationCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.Name).NotEmpty();
    }
}

internal class EditOrganizationHandler : IRequestHandler<EditOrganizationCommand, string>
{
    private readonly IDocumentStore _store;

    public EditOrganizationHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(EditOrganizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var organization = await session.LoadAsync<Domain.Organization>(command.Id, cancellationToken) ?? 
            throw new NotFoundException($"{nameof(Domain.Organization)} with '{command.Id}'not found");
        
        Guard.AgainstNotFound(organization, OrganizationErrors.NotFoundById(command.Id));

        organization.Update(command.Name);
        await session.StoreAsync(organization, cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return organization.Id;
    }
}