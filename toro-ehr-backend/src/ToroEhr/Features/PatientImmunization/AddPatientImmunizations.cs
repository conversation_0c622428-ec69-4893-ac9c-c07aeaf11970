using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.PatientImmunization;

public sealed record AddPatientImmunizationCommand(string Code, string DisplayName, DateOnly Date)
    : AuthRequest<Unit>;

internal sealed class SetPatientImmunizationsAuth : IAuth<AddPatientImmunizationCommand, Unit>
{
    public SetPatientImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class SetPatientImmunizationsCommandValidator : AbstractValidator<AddPatientImmunizationCommand>
{
    public SetPatientImmunizationsCommandValidator()
    {
        RuleFor(x => x.Code).NotEmpty();
        RuleFor(x => x.DisplayName).NotEmpty();
        RuleFor(x => x.Date).NotEmpty();
    }
}

internal sealed class SetPatientImmunizationsHandler : IRequestHandler<AddPatientImmunizationCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SetPatientImmunizationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(AddPatientImmunizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.PatientImmunization immunization =
            Domain.PatientImmunization.Create(_user.PatientId!, command.Code, command.DisplayName, command.Date);

        await session.StoreAsync(immunization, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}