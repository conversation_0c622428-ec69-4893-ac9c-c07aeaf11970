using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.PatientImmunization;

public sealed record DeletePatientImmunizationCommand(string Id) : AuthRequest<Unit>;

internal sealed class DeletePatientImmunizationsAuth : IAuth<DeletePatientImmunizationCommand, Unit>
{
    public DeletePatientImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class DeletePatientImmunizationCommandValidator : AbstractValidator<DeletePatientImmunizationCommand>
{
    public DeletePatientImmunizationCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

internal sealed class DeletePatientImmunizationHandler : IRequestHandler<DeletePatientImmunizationCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public DeletePatientImmunizationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(DeletePatientImmunizationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.PatientImmunization immunization = await session.LoadAsync<Domain.PatientImmunization>(command.Id, cancellationToken);

        if (immunization == null) return Unit.Value;
        
        session.Delete(immunization.Id);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}