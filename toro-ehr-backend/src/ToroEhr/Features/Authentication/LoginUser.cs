using System.Security.Authentication;
using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Authentication;

public sealed record LoginUserCommand(string Email, string Password) : AnonRequest<string>;

internal sealed class LoginUserCommandValidator : AbstractValidator<LoginUserCommand>
{
    public LoginUserCommandValidator()
    {
        RuleFor(x => x.Email).NotEmpty();
        RuleFor(x => x.Password).NotEmpty();
    }
}

internal sealed class LoginUserHandler : IRequestHandler<LoginUserCommand, string>
{
    private readonly IDocumentStore _store;

    public LoginUserHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(LoginUserCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var matchedUser = await session.Query<User>()
            .Where(u => u.PrimaryEmail == command.Email)
            .FirstOrDefaultAsync(token: cancellationToken);

        AuthenticationGuard.AgainstNull(matchedUser);

        var isValidPassword = BCrypt.Net.BCrypt.Verify(command.Password, matchedUser.Password);

        if (isValidPassword == false)
        {
            throw new AuthenticationException();
        }

        var lastUserSession = await session.Query<UserSession>()
            .OrderByDescending(x => x.CreatedAt)
            .Where(x => x.UserId == matchedUser.Id)
            .FirstOrDefaultAsync(token: cancellationToken);

        // on login take first organization until we introduce default one 
        LocationEmployee defaultEmployeeOrganization = await session.Query<LocationEmployee>()
            .FirstOrDefaultAsync(po => po.EmployeeId == matchedUser.EmployeeId, token: cancellationToken);

        var userSession = await StoreUserSession(cancellationToken, matchedUser.Id, defaultEmployeeOrganization, 
            lastUserSession?.UserRole ?? matchedUser.UserRoles.First(), session, command.Timestamp);
        
        await UpdateUserLogin(cancellationToken, matchedUser, session, command.Timestamp);

        await session.SaveChangesAsync(cancellationToken);

        return userSession.AccessToken;
    }

    private static async Task UpdateUserLogin(CancellationToken cancellationToken, User matchedUser,
        IAsyncDocumentSession session, DateTime commandTimestamp)
    {
        matchedUser.UpdateLastLogin(commandTimestamp);
        await session.StoreAsync(matchedUser, cancellationToken);
    }

    private static async Task<UserSession> StoreUserSession(CancellationToken cancellationToken, string userId,
        LocationEmployee? orgEmployee, string userRole, IAsyncDocumentSession session, DateTime commandTimestamp)
    {
        DateTime accessTokenExpiresAt = commandTimestamp.AddMinutes(30);
        DateTime refreshTokenExpiresAt = commandTimestamp.AddMonths(3);

        UserSession userSession = UserSession.Create(userId, orgEmployee?.OrganizationId, orgEmployee?.LocationId, userRole, Utils.GenerateAccessToken(),
            Utils.GenerateRefreshToken(), accessTokenExpiresAt, refreshTokenExpiresAt, commandTimestamp);

        await session.StoreAsync(userSession, cancellationToken);
        return userSession;
    }
}