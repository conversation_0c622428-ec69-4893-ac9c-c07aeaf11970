using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Authentication;

public sealed record SeedCommand : IRequest<string>;

internal sealed class SeedCommandHandler : IRequestHandler<SeedCommand, string>
{
    private readonly IDocumentStore _store;

    public SeedCommandHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(SeedCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        //SuperAdmin
        Domain.User superAdmin = Domain.User.Create("<EMAIL>", "Super", "Admin", null, null,
            ["SuperAdmin"]);
        await session.StoreAsync(superAdmin, cancellationToken);

        //Organization and Location
        Domain.Organization organization = Domain.Organization.Create("Test Company");
        Domain.Address locationAddress = Domain.Address.Create("Test Street", "Test City", "NY", "12345");

        Domain.Location location = Domain.Location.Create(organization.Id, "Test Location", null, true, "", "", 0, 0, 48,
            locationAddress, null);
        await session.StoreAsync(organization, cancellationToken);
        await session.StoreAsync(location, cancellationToken);


        //OrganizationAdmin
        Domain.Address orgAdminAddress = Domain.Address.Create("Test Street", "Test City", "NY", "12345");
        Domain.Employee orgAdmin = Domain.Employee.Create("<EMAIL>",
            "Test", "OrgAdmin", "*********", "12345678", orgAdminAddress);
        Domain.LocationEmployee orgAdminLocationEmployee =
            Domain.LocationEmployee.Create(organization.Id, location.Id, orgAdmin.Id, ["OrganizationAdmin"],
                "0000FF", "America/Denver", Utils.GenerateRandomId(), DateTime.UtcNow, false);
        Domain.User orgAdminUser = Domain.User.Create("<EMAIL>", "Test", "Doctor", null,
            orgAdmin.Id,
            ["Employee"]);

        await session.StoreAsync(orgAdminUser, cancellationToken);
        await session.StoreAsync(orgAdmin, cancellationToken);
        await session.StoreAsync(orgAdminLocationEmployee, cancellationToken);


        //LocationAdmin
        Domain.Address locAdminAddress = Domain.Address.Create("Test Street", "Test City", "NY", "12345");
        Domain.Employee locAdmin = Domain.Employee.Create("<EMAIL>",
            "Test", "LocAdmin", "*********", "12345678", locAdminAddress);
        Domain.LocationEmployee locAdminLocationEmployee =
            Domain.LocationEmployee.Create(organization.Id, location.Id, locAdmin.Id, ["LocationAdmin"],
                "0000FF", "America/Denver", Utils.GenerateRandomId(), DateTime.UtcNow, false);
        Domain.User locAdminUser = Domain.User.Create("<EMAIL>", "Test", "LocationAdmin", null,
            locAdmin.Id,
            ["Employee"]);

        await session.StoreAsync(locAdminUser, cancellationToken);
        await session.StoreAsync(locAdmin, cancellationToken);
        await session.StoreAsync(locAdminLocationEmployee, cancellationToken);

        //Practitioner
        Domain.Address practitionerAddress = Domain.Address.Create("Test Street", "Test City", "NY", "12345");
        Domain.Employee practitioner = Domain.Employee.Create("<EMAIL>",
            "Test", "Practitioner", "*********", "12345678", practitionerAddress);
        Domain.LocationEmployee practitionerLocationEmployee =
            Domain.LocationEmployee.Create(organization.Id, location.Id, practitioner.Id, ["Practitioner"],
                "0000FF", "America/Denver", Utils.GenerateRandomId(), DateTime.UtcNow, false);
        Domain.User practitionerUser = Domain.User.Create("<EMAIL>", "Test", "Practitioner", null,
            practitioner.Id,
            ["Employee"]);

        await session.StoreAsync(practitionerUser, cancellationToken);
        await session.StoreAsync(practitioner, cancellationToken);
        await session.StoreAsync(practitionerLocationEmployee, cancellationToken);


        //Nurse
        Domain.Address nurseAddress = Domain.Address.Create("Test Street", "Test City", "NY", "12345");
        Domain.Employee nurse = Domain.Employee.Create("<EMAIL>",
            "Test", "OrgAdmin", "*********", "12345678", nurseAddress);
        Domain.LocationEmployee nurseLocationEmployee =
            Domain.LocationEmployee.Create(organization.Id, location.Id, nurse.Id, ["Nurse"],
                "0000FF", "America/Denver", Utils.GenerateRandomId(), DateTime.UtcNow, false);
        Domain.User nurseUser = Domain.User.Create("<EMAIL>", "Test", "Nurse", null, nurse.Id, ["Employee"]);

        await session.StoreAsync(nurseUser, cancellationToken);
        await session.StoreAsync(nurse, cancellationToken);
        await session.StoreAsync(nurseLocationEmployee, cancellationToken);

        //FrontDesk
        Domain.Address frontDeskAddress = Domain.Address.Create("Test Street", "Test City", "NY", "12345");
        Domain.Employee frontDesk = Domain.Employee.Create("<EMAIL>",
            "Test", "FrontDesk", "*********", "12345678", frontDeskAddress);
        Domain.LocationEmployee frontDeskLocationEmployee =
            Domain.LocationEmployee.Create(organization.Id, location.Id, frontDesk.Id, ["Nurse"],
                "0000FF", "America/Denver", Utils.GenerateRandomId(), DateTime.UtcNow, false);
        Domain.User frontDeskUser = Domain.User.Create("<EMAIL>", "Test", "Nurse", null, frontDesk.Id,
            ["Employee"]);

        await session.StoreAsync(frontDeskUser, cancellationToken);
        await session.StoreAsync(frontDesk, cancellationToken);
        await session.StoreAsync(frontDeskLocationEmployee, cancellationToken);


        //Patient
        Domain.Patient patient = Domain.Patient.Create(
            await MrnService.GenerateUniqueMrnAsync(session, cancellationToken), "<EMAIL>",
            "Test", "Patient", DateTime.UtcNow.AddYears(-35), "*********");
        Domain.OrganizationPatient orgPatient =
            Domain.OrganizationPatient.Create(organization.Id, patient.Id, Utils.GenerateRandomId(), DateTime.UtcNow);
        Domain.User orgPatientUser = Domain.User.Create("<EMAIL>", "Test", "Patient", patient.Id, null,
            ["Patient"]);

        await session.StoreAsync(patient, cancellationToken);
        await session.StoreAsync(orgPatient, cancellationToken);
        await session.StoreAsync(orgPatientUser, cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return "done";
    }
}