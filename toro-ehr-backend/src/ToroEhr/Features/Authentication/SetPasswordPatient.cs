using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Authentication;

public sealed record SetPasswordPatientCommand(string InvitationToken, string Password) : AnonRequest<string>;

internal sealed class SetPasswordPatientValidator : AbstractValidator<SetPasswordPatientCommand>
{
    public SetPasswordPatientValidator()
    {
        RuleFor(x => x.InvitationToken).NotEmpty();
        RuleFor(x => x.Password).NotEmpty();
    }
}

internal sealed class SetPasswordPatientHandler : IRequestHandler<SetPasswordPatientCommand, string>
{
    private readonly IDocumentStore _store;

    public SetPasswordPatientHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(SetPasswordPatientCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var matchedOrgPatient = await session.Query<OrganizationPatient>()
            .Include(x => x.PatientId)
            .Where(u => u.InvitationToken == command.InvitationToken)
            .FirstOrDefaultAsync(token: cancellationToken);

        Guard.AgainstNotFound(matchedOrgPatient,
            new("Invitation.NotFound", $"Invitation with the token: '{command.InvitationToken}' was not found"));

        var matchedUser = await session.Query<User>()
            .FirstOrDefaultAsync(u => u.PatientId == matchedOrgPatient.PatientId,
                token: cancellationToken);

        matchedUser.SetPassword(command.Password);
        matchedUser.UpdateLastLogin(command.Timestamp);
        matchedOrgPatient.AcceptInvitation(command.Timestamp);

        var userSession = await StoreUserSession(cancellationToken, matchedUser.Id, session, command.Timestamp);

        await session.SaveChangesAsync(cancellationToken);

        return userSession.AccessToken;
    }

    private static async Task<UserSession> StoreUserSession(CancellationToken cancellationToken, string userId,
        IAsyncDocumentSession session, DateTime commandTimestamp)
    {
        DateTime accessTokenExpiresAt = commandTimestamp.AddMinutes(30);
        DateTime refreshTokenExpiresAt = commandTimestamp.AddMonths(3);

        UserSession userSession = UserSession.Create(userId, null, null, UserRole.Patient, Utils.GenerateAccessToken(),
            Utils.GenerateRefreshToken(), accessTokenExpiresAt, refreshTokenExpiresAt, commandTimestamp);

        await session.StoreAsync(userSession, cancellationToken);
        return userSession;
    }
}