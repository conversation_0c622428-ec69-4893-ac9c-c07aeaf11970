using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Enums;

namespace ToroEhr.Features.Authentication;

public sealed record UpdateSessionCommand(string SelectedUserRole, string? SelectedOrganizationId, string? SelectedLocationId) : AnonRequest<Unit>;

internal sealed class UpdateSessionCommandValidator : AbstractValidator<UpdateSessionCommand>
{
    public UpdateSessionCommandValidator()
    {
        RuleFor(x => x.SelectedUserRole).NotEmpty();
    }
}

internal sealed class UpdateSessionHandler : IRequestHandler<UpdateSessionCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public UpdateSessionHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(UpdateSessionCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var user = await session.LoadAsync<User>(_user.UserId, cancellationToken);

        var userSession = await session.LoadAsync<UserSession>(_user.SessionId, cancellationToken);
        AuthenticationGuard.AgainstNull(userSession);

        var locationId = command.SelectedLocationId;
        var organizationId = command.SelectedOrganizationId;
        if (command.SelectedUserRole == UserRole.Employee && locationId == null && organizationId == null)
        {
            var locationEmployee = await session.Query<LocationEmployee>()
                .Where(e => e.EmployeeId == _user.EmployeeId)
                .FirstOrDefaultAsync(cancellationToken);

            locationId = locationEmployee.LocationId;
            organizationId = locationEmployee.OrganizationId;
        }

        userSession.Update(command.SelectedUserRole, organizationId, locationId);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}