using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Authentication;

public sealed record GetUserInfoQuery : AuthRequest<UserInfoResponse>;

public sealed record UserInfoResponse(
    string UserId,
    string Email,
    string FirstName,
    string LastName,
    string SelectedUserRole,
    string? EmployeeId,
    string? PatientId,
    string? OrganizationId,
    string? OrganizationName,
    string? LocationId,
    string? LocationName,
    IEnumerable<string>? LocationEmployeeRoles,
    IEnumerable<AvailableLocationResponse> AvailableLocations,
    IEnumerable<string> AvailableUserRoles
    );

public sealed record AvailableLocationResponse(string OrganizationId, string? OrganizationName, string LocationId, string? LocationName);
internal class GetUserInfoAuth : IAuth<GetUserInfoQuery, UserInfoResponse>
{
}

internal class GetUserInfoHandler : IRequestHandler<GetUserInfoQuery, UserInfoResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetUserInfoHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<UserInfoResponse> Handle(GetUserInfoQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var user = await session.LoadAsync<User>(_user.UserId, cancellationToken);

        Domain.Location? selectedLocation = null;
        Domain.Organization? selectedOrganization = null;
        LocationEmployee? selectedLocationEmployee = null;
        List<AvailableLocationResponse> availableLocations = new List<AvailableLocationResponse>();

        if (_user.SelectedUserRole == UserRole.Employee)
        {
            var locationEmplyees = await session
            .Query<LocationEmployee>()
            .Include(op => op.OrganizationId)
            .Include(op => op.LocationId)
            .Where(op => op.EmployeeId == _user.EmployeeId)
            .ToListAsync(cancellationToken);

            foreach (var locationEmplyee in locationEmplyees)
            {
                var organization = await session.LoadAsync<Domain.Organization>(locationEmplyee!.OrganizationId, cancellationToken);
                var location = await session.LoadAsync<Domain.Location>(locationEmplyee!.LocationId, cancellationToken);

                if (locationEmplyee.LocationId == _user.SelectedLocationId)
                {
                    selectedLocationEmployee = locationEmplyee;
                    selectedOrganization = organization;
                    selectedLocation = location;
                }
                else
                {
                    availableLocations.Add(new AvailableLocationResponse(organization.Id, organization.Name, location.Id, location.Name));
                }
            }
        }

        return new UserInfoResponse(user.Id, _user.Email, _user.FirstName, _user.LastName, _user.SelectedUserRole,
            _user.EmployeeId, _user.PatientId, selectedOrganization?.Id, selectedOrganization?.Name, selectedLocation?.Id, selectedLocation?.Name,
            selectedLocationEmployee?.EmployeeRoles, availableLocations, user.UserRoles);
    }
}