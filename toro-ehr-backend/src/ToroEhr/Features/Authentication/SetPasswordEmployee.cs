using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Authentication;

public sealed record SetPasswordEmployeeCommand(string InvitationToken, string Password) : AnonRequest<string>;

internal sealed class SetPasswordValidator : AbstractValidator<SetPasswordEmployeeCommand>
{
    public SetPasswordValidator()
    {
        RuleFor(x => x.InvitationToken).NotEmpty();
        RuleFor(x => x.Password).NotEmpty();
    }
}

internal sealed class SetPasswordEmployeeHandler : IRequestHandler<SetPasswordEmployeeCommand, string>
{
    private readonly IDocumentStore _store;

    public SetPasswordEmployeeHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<string> Handle(SetPasswordEmployeeCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var matchedOrgEmployee = await session.Query<LocationEmployee>()
            .Include(x => x.EmployeeId)
            .Where(u => u.InvitationToken == command.InvitationToken)
            .FirstOrDefaultAsync(token: cancellationToken);

        Guard.AgainstNotFound(matchedOrgEmployee,
            new("Invitation.NotFound", $"Invitation with the token: '{command.InvitationToken}' was not found"));

        var matchedUser = await session.Query<User>()
            .FirstOrDefaultAsync(u => u.EmployeeId == matchedOrgEmployee.EmployeeId,
                token: cancellationToken);

        matchedUser.SetPassword(command.Password);
        matchedUser.UpdateLastLogin(command.Timestamp);
        matchedOrgEmployee.AcceptInvitation(command.Timestamp);

        var userSession = await StoreUserSession(cancellationToken, matchedUser.Id,
            matchedOrgEmployee, session, command.Timestamp);

        await session.SaveChangesAsync(cancellationToken);

        return userSession.AccessToken;
    }

    private static async Task<UserSession> StoreUserSession(CancellationToken cancellationToken, string userId,
        LocationEmployee locationEmployee, IAsyncDocumentSession session, DateTime commandTimestamp)
    {
        DateTime accessTokenExpiresAt = commandTimestamp.AddMinutes(30);
        DateTime refreshTokenExpiresAt = commandTimestamp.AddMonths(3);

        UserSession userSession = UserSession.Create(userId, locationEmployee.OrganizationId,
            locationEmployee.LocationId, UserRole.Employee,
            Utils.GenerateAccessToken(), Utils.GenerateRefreshToken(), accessTokenExpiresAt, refreshTokenExpiresAt,
            commandTimestamp);

        await session.StoreAsync(userSession, cancellationToken);
        return userSession;
    }
}