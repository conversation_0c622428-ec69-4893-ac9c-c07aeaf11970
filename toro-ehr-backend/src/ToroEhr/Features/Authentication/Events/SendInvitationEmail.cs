using HandlebarsDotNet;
using MediatR;
using ToroEhr.Domain;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Templates.Email.SetPassword;

namespace ToroEhr.Features.Authentication.Events;

public record SendInvitationEmail(string RecipientFirstName, string RecipientLastName, string RecipientEmail,
        UserRequestSession User, string InvitationToken) : BaseEventEntity;

public class SendInvitationEmailEventHandler : INotificationHandler<SendInvitationEmail>
{
    private readonly EmailService _emailService;

    public SendInvitationEmailEventHandler(EmailService emailService)
    {
        _emailService = emailService;
    }
    public async Task Handle(SendInvitationEmail notification, CancellationToken cancellationToken = new())
    {
        var rootDir = Path.Combine("Templates", "Email", "SetPassword");
        var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        var compiledTemplate = Handlebars.Compile(htmlTemplate);

        string email = compiledTemplate(new SetPasswordEmail(Config.Application.Name, Config.Application.Url,
        $"{notification.RecipientFirstName} {notification.RecipientLastName}", notification.User.FullName, notification.User.OrganizationName!,
            $"{Config.Application.Url}/set-password-patient?invitationToken={notification.InvitationToken}"));
        string subject = "Set up account";
        await _emailService.SendEmailAsync(notification.RecipientEmail, subject, email);

        //var rootDir = Path.Combine("Templates", "Email", "SetPassword");
        //var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        //var compiledTemplate = Handlebars.Compile(htmlTemplate);

        //string email = compiledTemplate(new SetPasswordEmail(Config.Application.Name, Config.Application.Url,
        //    employee.FirstName, _user.FullName, _user.OrganizationName!,
        //    $"{Config.Application.Url}/set-password-Employee?invitationToken={organizationEmployee.InvitationToken}"));
        //string subject = "Set up account";
        //await _emailService.SendEmailAsync(employee.Email, subject, email);
    }
}