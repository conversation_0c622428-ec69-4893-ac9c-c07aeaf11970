using MediatR;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Authentication.Events;

public record SendInvitationSms(Domain.Patient Patient,
    UserRequestSession User, string InvitationToken) : BaseEventEntity;

public class SendInvitationSmsEventHandler : INotificationHandler<SendInvitationSms>
{
    private readonly SmsService _smsService;

    public SendInvitationSmsEventHandler(SmsService smsService)
    {
        _smsService = smsService;
    }
    public async Task Handle(SendInvitationSms notification, CancellationToken cancellationToken = new())
    {
        var message = $"{notification.User.FullName} with {notification.User.OrganizationName!} has invited you to use {Config.Application.Name} to collaborate with them. \n {Config.Application.Url}/set-password-patient?invitationToken={notification.InvitationToken}";
        await _smsService.SendSmsAsync(notification.Patient.PhoneNumbers.First(x => x.IsPrimary).Number, message);
    }
}