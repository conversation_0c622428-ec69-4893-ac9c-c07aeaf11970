using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Features.Questionnaire.Shared;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Questionnaire;

public sealed record ListPatientQuestionnairesQuery(string Placement, string? EncounterId)
    : AuthRequest<IEnumerable<PatientQuestionnaireResponse>>;

public sealed record PatientQuestionnaireResponse(
    string Id,
    string Title,
    string LocationName,
    string OrganizationName,
    IEnumerable<QuestionResponse> Questions);

internal sealed class
    ListPatientQuestionnairesAuth : IAuth<ListPatientQuestionnairesQuery, IEnumerable<PatientQuestionnaireResponse>>
{
    public ListPatientQuestionnairesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    ListPatientQuestionnairesHandler : IRequestHandler<ListPatientQuestionnairesQuery,
    IEnumerable<PatientQuestionnaireResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientQuestionnairesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<IEnumerable<PatientQuestionnaireResponse>> Handle(ListPatientQuestionnairesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        string patientId;
        if (_user.IsPatient)
        {
            patientId = _user.PatientId!;
        }
        else
        {
            var encounter = await session.LoadAsync<Domain.Encounter>(query.EncounterId, cancellationToken);
            patientId = encounter.PatientId;
        }

        var patientOrganizations = await session.Query<OrganizationPatient>().Where(x => x.PatientId == patientId)
            .ToListAsync(token: cancellationToken);

        IEnumerable<Questionnaires_ByLocation.Entry> locationQuestionnaires = await session
            .Query<Questionnaires_ByLocation.Entry, Questionnaires_ByLocation>()
            .ProjectInto<Questionnaires_ByLocation.Entry>()
            .Include(x => x.QuestionnaireId)
            .Where(x => x.OrganizationId.In(patientOrganizations.Select(po => po.OrganizationId).ToList()) &&
                        x.Placement == query.Placement)
            .ToListAsync(token: cancellationToken);

        var result = new List<PatientQuestionnaireResponse>();

        var responses = await session.Query<Domain.QuestionnaireResponse>().Where(x =>
            x.QuestionnaireId.In(locationQuestionnaires.Select(lq => lq.QuestionnaireId)) &&
            x.PatientId == _user.PatientId).ToListAsync(token: cancellationToken);

        foreach (var entry in locationQuestionnaires)
        {
            var questionnaire = await session.LoadAsync<Domain.Questionnaire>(entry.QuestionnaireId, cancellationToken);

            var questionnaireResponse = responses.FirstOrDefault(x => x.QuestionnaireId == entry.QuestionnaireId);

            result.Add(new PatientQuestionnaireResponse
            (
                entry.QuestionnaireId,
                entry.Title,
                entry.LocationName,
                entry.OrganizationName,
                questionnaire.Questions.Select(q =>
                    new QuestionResponse(q.Id, q.Text, q.Type, q.IsRequired, q.Options,
                        questionnaireResponse?.Answers.FirstOrDefault(a => a.QuestionId == q.Id)?.Values ?? []))));
        }

        return result;
    }
}