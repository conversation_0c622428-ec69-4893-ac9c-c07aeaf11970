using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Features.Questionnaire.Shared;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using Utils = ToroEhr.Shared.Utils;

namespace ToroEhr.Features.Questionnaire;

public sealed record UpdateQuestionnaireCommand(
    string Id,
    string Title,
    string Type,
    string Placement,
    IEnumerable<QuestionRequest> Questions) : AuthRequest<Unit>;

internal sealed class UpdateQuestionnaireAuth : IAuth<UpdateQuestionnaireCommand, Unit>
{
    public UpdateQuestionnaireAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public sealed class UpdateQuestionnaireCommandValidator : AbstractValidator<UpdateQuestionnaireCommand>
{
    public UpdateQuestionnaireCommandValidator()
    {
        RuleFor(x => x.Title).NotEmpty();
        RuleFor(x => x.Type).NotEmpty();
        RuleFor(x => x.Placement).NotEmpty();
        RuleFor(x => x.Questions)
            .NotEmpty().WithMessage("At least one question is required.");

        RuleForEach(x => x.Questions).ChildRules(question =>
        {
            question.RuleFor(q => q.Text).NotEmpty();

            question.RuleFor(q => q.Options)
                .NotEmpty()
                .When(q => !string.Equals(q.Type, "FreeText", StringComparison.OrdinalIgnoreCase))
                .WithMessage("Options must be provided for Single Choice and Multiple Choice questions.");
        });
    }
}

internal sealed class UpdateQuestionnaireHandler : IRequestHandler<UpdateQuestionnaireCommand, Unit>
{
    private readonly IDocumentStore _store;

    public UpdateQuestionnaireHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
    }

    public async Task<Unit> Handle(UpdateQuestionnaireCommand command, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        Domain.Questionnaire questionnaire =
            await session.LoadAsync<Domain.Questionnaire>(command.Id, cancellationToken);

        Guard.AgainstNotFound(questionnaire,
            new AppError("Questionnaire.NotFound", $"Questionnaire with 'id:' {command.Id} not found!"));

        questionnaire.Update(command.Title, command.Type, command.Placement,
            command.Questions.Select(q =>
                new Question(q.Id.IsNullOrWhiteSpace() ? Utils.GenerateRandomId() : q.Id, q.Text, q.Type, q.IsRequired,
                    q.Options ?? [])).ToList());
        
        IDictionary<string, DocumentsChanges[]> changes = session.Advanced.WhatChanged();

        await session.StoreAsync(questionnaire, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}