using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.CptCodes;

public sealed record ListCptCodesQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CptCodeResponse>>;

public sealed record CptCodeResponse(string Id, string Code, string ConceptId, string ClinicianDescriptor);

internal sealed class ListCptCodesAuth : IAuth<ListCptCodesQuery, PaginatedList<CptCodeResponse>>
{
    public ListCptCodesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListCptCodesHandler : IRequestHandler<ListCptCodesQuery, PaginatedList<CptCodeResponse>>
{
    private readonly IDocumentStore _store;
    public ListCptCodesHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CptCodeResponse>> Handle(ListCptCodesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.CptCode> dbQuery = session.Query<Domain.CptCode>();

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.Code, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.ClinicianDescriptor, $"{query.PagedSearchParams.SearchParam}*");
        }

        var cptCodes = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<CptCodeResponse>.Create(
            cptCodes.Select(x => new CptCodeResponse(x.Id, x.Code, x.ConceptId,
            x.ClinicianDescriptor)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}
