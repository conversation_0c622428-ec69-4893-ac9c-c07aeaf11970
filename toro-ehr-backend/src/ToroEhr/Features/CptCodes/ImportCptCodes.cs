using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;
using ClosedXML.Excel;

namespace ToroEhr.Features.CptCodes;

public sealed record ImportCptCodesCommand(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportCptCodesCommandValidator : AbstractValidator<ImportCptCodesCommand>
{
    public ImportCptCodesCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportCptCodesAuth : IAuth<ImportCptCodesCommand, Unit>
{
    public ImportCptCodesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportCptHandler : IRequestHandler<ImportCptCodesCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportCptHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportCptCodesCommand request, CancellationToken cancellationToken)
    {
        var cptCodes = await ParseTextFileAsync(request.File);

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        foreach (var cptCode in cptCodes)
        {
            await session.StoreAsync(cptCode, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<List<CptCode>> ParseTextFileAsync(IFormFile file)
    {
        var records = new List<CptCode>();
        var sheetName = "Sheet0";

        using (var stream = new MemoryStream())
        {
            await file.CopyToAsync(stream);
            stream.Position = 0; // Reset stream position

            using (var workbook = new XLWorkbook(stream))
            {
                var worksheet = workbook.Worksheet(sheetName);
                Guard.AgainstNotFound(worksheet, new("Worksheet.NotFound", $"Worksheet with 'name:' {sheetName} not found!"));

                int startRow = 2; // Start reading from row 2 - skip header

                foreach (var row in worksheet.RowsUsed().Skip(startRow - 1))
                {
                    var data = CptCode.Create(
                        code: row.Cell(2).GetValue<string>(), // Column B
                        conceptId: row.Cell(1).GetValue<string>(), // Column A
                        clinicianDescriptor: row.Cell(3).GetValue<string>() // Column C
                    );

                    records.Add(data);
                }
            }
        }

        return records;
    }
}