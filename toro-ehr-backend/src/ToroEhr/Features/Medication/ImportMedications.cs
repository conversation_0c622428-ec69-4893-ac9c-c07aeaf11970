using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using Raven.Client.Documents.Operations;

namespace ToroEhr.Features.Medication;

public sealed record ImportMedicationsCommand(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportMedicationsCommandValidator : AbstractValidator<ImportMedicationsCommand>
{
    public ImportMedicationsCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportMedicationsAuth : IAuth<ImportMedicationsCommand, Unit>
{    public ImportMedicationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportMedicationsHandler : IRequestHandler<ImportMedicationsCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportMedicationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportMedicationsCommand request, CancellationToken cancellationToken)
    {
        var (medications, relations, attributes) = await CodeService.ParseRxNormZipAsync(request.File);

        using var bulkInsert = _store.BulkInsert();

        foreach (var medication in medications)
        {
            await bulkInsert.StoreAsync(medication);
        }
        foreach (var relation in relations)
        {
            await bulkInsert.StoreAsync(relation);
        }
        await _store.Operations.SendAsync(new DeleteByQueryOperation("from MedicationAttributes"));
        foreach (var attribute in attributes)
        {
            await bulkInsert.StoreAsync(attribute);
        }

        return Unit.Value;
    }
}