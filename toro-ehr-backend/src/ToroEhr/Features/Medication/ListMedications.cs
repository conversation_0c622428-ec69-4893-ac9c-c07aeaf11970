using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Medication;

public sealed record ListMedicationsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CodingResponse>>;

internal sealed class ListMedicationAuth : IAuth<ListMedicationsQuery, PaginatedList<CodingResponse>>
{
    public ListMedicationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListLocationsHandler : IRequestHandler<ListMedicationsQuery, PaginatedList<CodingResponse>>
{
    private readonly IDocumentStore _store;

    public ListLocationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CodingResponse>> Handle(ListMedicationsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.Medication> dbQuery =
            session.Query<Domain.Medication>().Where(x => x.SourceAbbreviation == "RXNORM");

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.SourceCode, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.TermString, $"{query.PagedSearchParams.SearchParam}*");
        }

        var medications = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<CodingResponse>.Create(
            medications.Select(x => new CodingResponse(x.Id, x.SourceCode, string.Empty, x.SourceAbbreviation,
                string.Empty, x.TermString)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}