using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Medication;

public sealed record GetMedicationsByIngredientQuery(string IngredientId)
    : AuthRequest<List<SelectListItem>>;

internal sealed class GetMedicationsByIngredientAuth : IAuth<GetMedicationsByIngredientQuery, List<SelectListItem>>
{
    public GetMedicationsByIngredientAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class GetMedicationsByIngredientHandler : IRequestHandler<GetMedicationsByIngredientQuery, List<SelectListItem>>
{
    private readonly IDocumentStore _store;
    public GetMedicationsByIngredientHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<List<SelectListItem>> Handle(GetMedicationsByIngredientQuery query,
        CancellationToken cancellationToken)
    {
        var result = new List<SelectListItem>();
        using var session = _store.OpenAsyncSession();

        var ingredient = await session.Query<Domain.Medication>()
            .Where(x => x.RxCui == query.IngredientId && (x.TermType == "IN" || x.TermType == "MIN"))
            .FirstOrDefaultAsync(cancellationToken);

        if (ingredient == null) 
        {
            return result;
        }

        var medications = await session.Query<Domain.Medication>()
             .Where(x => x.TermType == "SCD" || x.TermType == "GPCK")
             .Search(x => x.TermString, $"*{ingredient!.TermString}*")
            .ToListAsync(cancellationToken);


        foreach (var medication in medications)
        {
            result.Add(new SelectListItem(medication.TermString, medication.Id));
        }

        return result;
    }
}