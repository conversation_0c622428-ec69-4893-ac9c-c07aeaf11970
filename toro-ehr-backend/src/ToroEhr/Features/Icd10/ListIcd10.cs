using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;

namespace ToroEhr.Features.Icd10;

public sealed record ListIcd10Query(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CodingResponse>>;

internal sealed class ListIcd10Auth : IAuth<ListIcd10Query, PaginatedList<CodingResponse>>
{
    public ListIcd10Auth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListIcd10Handler : IRequestHandler<ListIcd10Query, PaginatedList<CodingResponse>>
{
    private readonly IDocumentStore _store;
    public ListIcd10Handler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CodingResponse>> Handle(ListIcd10Query query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.Icd10> dbQuery = session.Query<Domain.Icd10>();

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.Code, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.DisplayName, $"{query.PagedSearchParams.SearchParam}*");
        }

        var icd10s = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<CodingResponse>.Create(
            icd10s.Select(x => new CodingResponse(x.Id, x.Code, x.CodeSystem, x.CodeSystemName,
            x.CodeSystemVersion, x.DisplayName)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}