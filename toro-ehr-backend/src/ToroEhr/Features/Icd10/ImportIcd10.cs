using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.Icd10;

public sealed record ImportIcd10Command(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportIcd10CommandValidator : AbstractValidator<ImportIcd10Command>
{
    public ImportIcd10CommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportIcd10Auth : IAuth<ImportIcd10Command, Unit>
{
    public ImportIcd10Auth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportIcd10Handler : IRequestHandler<ImportIcd10Command, Unit>
{
    private readonly IDocumentStore _store;

    public ImportIcd10Handler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportIcd10Command request, CancellationToken cancellationToken)
    {
        var icd10s = await ParseTextFileAsync(request.File);

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        foreach (var icd10 in icd10s)
        {
            var icd10Code = Domain.Icd10.Create(icd10.Code, icd10.CodeSystem, icd10.CodeSystemName,
                icd10.CodeSystemVersion, icd10.DisplayName);
            await session.StoreAsync(icd10Code, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<List<CodingRecord>> ParseTextFileAsync(IFormFile file)
    {
        var diseases = new List<CodingRecord>();

        using (var stream = new StreamReader(file.OpenReadStream()))
        {
            while (!stream.EndOfStream)
            {
                var line = await stream.ReadLineAsync();
                if (!string.IsNullOrWhiteSpace(line))
                {
                    diseases.Add(new CodingRecord
                    {
                        Code = line.Substring(0, 8).Trim(),   // First 8 chars for Code
                        DisplayName = line.Substring(8).Trim() // Remaining text for Description
                    });
                }
            }
        }

        return diseases;
    }
}