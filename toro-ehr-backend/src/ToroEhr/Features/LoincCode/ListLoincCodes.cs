using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;

namespace ToroEhr.Features.LoincCode;

public sealed record ListLoincCodesQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CodingResponse>>;

internal sealed class ListImmunizationsAuth : IAuth<ListLoincCodesQuery, PaginatedList<CodingResponse>>
{
    public ListImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListImmunizationsHandler : IRequestHandler<ListLoincCodesQuery, PaginatedList<CodingResponse>>
{
    private readonly IDocumentStore _store;
    public ListImmunizationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CodingResponse>> Handle(ListLoincCodesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.LoincCode> dbQuery = session.Query<Domain.LoincCode>();

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.LoincNum, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.Component, $"{query.PagedSearchParams.SearchParam}*");
        }

        var allergies = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<CodingResponse>.Create(
            allergies.Select(x => new CodingResponse(x.Id, x.LoincNum ?? string.Empty, string.Empty, string.Empty,
            string.Empty, x.Component ?? string.Empty)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}