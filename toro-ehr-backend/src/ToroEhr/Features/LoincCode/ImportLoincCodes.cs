using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using System.Globalization;
using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;

namespace ToroEhr.Features.LoincCode;

public sealed record ImportLoincCodesCommand(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportLoincCodesCommandCommandValidator : AbstractValidator<ImportLoincCodesCommand>
{
    public ImportLoincCodesCommandCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportLoincCodesAuth : IAuth<ImportLoincCodesCommand, Unit>
{
    public ImportLoincCodesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportLoincCodesHandler : IRequestHandler<ImportLoincCodesCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportLoincCodesHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportLoincCodesCommand request, CancellationToken cancellationToken)
    {
        var loincEntries = await ParseCsvFileAsync(request.File);

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        foreach (var loincEntry in loincEntries)
        {
            var loincCode = Domain.LoincCode.Create(loincEntry);
            await session.StoreAsync(loincCode, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<List<LoincEntry>> ParseCsvFileAsync(IFormFile file)
    {
        await using var stream = file.OpenReadStream();
        using var reader = new StreamReader(stream);
        using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            HasHeaderRecord = true,
        });

        var records = csv.GetRecords<LoincEntry>().ToList();


        return records;
    }
}

public class LoincEntry
{
    [Name("LOINC_NUM")]
    public string? LoincNum { get; set; }

    [Name("COMPONENT")]
    public string? Component { get; set; }

    [Name("PROPERTY")]
    public string? Property { get; set; }

    [Name("TIME_ASPCT")]
    public string? TimeAspct { get; set; }

    [Name("SYSTEM")]
    public string? System { get; set; }

    [Name("SCALE_TYP")]
    public string? ScaleTyp { get; set; }

    [Name("METHOD_TYP")]
    public string? MethodTyp { get; set; }

    [Name("CLASS")]
    public string? Class { get; set; }

    [Name("VersionLastChanged")]
    public string? VersionLastChanged { get; set; }

    [Name("CHNG_TYPE")]
    public string? ChngType { get; set; }

    [Name("DefinitionDescription")]
    public string? DefinitionDescription { get; set; }

    [Name("STATUS")]
    public string? Status { get; set; }

    [Name("CONSUMER_NAME")]
    public string? ConsumerName { get; set; }

    [Name("CLASSTYPE")]
    public string? Classtype { get; set; }

    [Name("FORMULA")]
    public string? Formula { get; set; }

    [Name("EXMPL_ANSWERS")]
    public string? ExmplAnswers { get; set; }

    [Name("SURVEY_QUEST_TEXT")]
    public string? SurveyQuestText { get; set; }

    [Name("SURVEY_QUEST_SRC")]
    public string? SurveyQuestSrc { get; set; }

    [Name("UNITSREQUIRED")]
    public string? UnitsRequired { get; set; }

    [Name("RELATEDNAMES2")]
    public string? RelatedNames2 { get; set; }

    [Name("SHORTNAME")]
    public string? ShortName { get; set; }

    [Name("ORDER_OBS")]
    public string? OrderObs { get; set; }

    [Name("HL7_FIELD_SUBFIELD_ID")]
    public string? Hl7FieldSubfieldId { get; set; }

    [Name("EXTERNAL_COPYRIGHT_NOTICE")]
    public string? ExternalCopyrightNotice { get; set; }

    [Name("EXAMPLE_UNITS")]
    public string? ExampleUnits { get; set; }

    [Name("LONG_COMMON_NAME")]
    public string? LongCommonName { get; set; }

    [Name("EXAMPLE_UCUM_UNITS")]
    public string? ExampleUcumUnits { get; set; }

    [Name("STATUS_REASON")]
    public string? StatusReason { get; set; }

    [Name("STATUS_TEXT")]
    public string? StatusText { get; set; }

    [Name("CHANGE_REASON_PUBLIC")]
    public string? ChangeReasonPublic { get; set; }

    [Name("COMMON_TEST_RANK")]
    public int? CommonTestRank { get; set; }

    [Name("COMMON_ORDER_RANK")]
    public int? CommonOrderRank { get; set; }

    [Name("HL7_ATTACHMENT_STRUCTURE")]
    public string? Hl7AttachmentStructure { get; set; }

    [Name("EXTERNAL_COPYRIGHT_LINK")]
    public string? ExternalCopyrightLink { get; set; }

    [Name("PanelType")]
    public string? PanelType { get; set; }

    [Name("AskAtOrderEntry")]
    public string? AskAtOrderEntry { get; set; }

    [Name("AssociatedObservations")]
    public string? AssociatedObservations { get; set; }

    [Name("VersionFirstReleased")]
    public string? VersionFirstReleased { get; set; }

    [Name("ValidHL7AttachmentRequest")]
    public string? ValidHl7AttachmentRequest { get; set; }

    [Name("DisplayName")]
    public string? DisplayName { get; set; }
}