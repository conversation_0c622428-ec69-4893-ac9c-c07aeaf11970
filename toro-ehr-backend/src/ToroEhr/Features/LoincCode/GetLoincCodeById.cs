using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.LoincCode;

public sealed record GetLoincCodeByIdQuery(string id)
    : AuthRequest<LoincCodeDetailsResponse>;

public record LoincCodeDetailsResponse(
    string? id, string? LoincNum, string? Component, string? Property, string? TimeAspct, string? System, string? ScaleTyp, string? MethodTyp,
    string? Class, string? VersionLastChanged, string? ChngType, string? DefinitionDescription, string? Status, string? ConsumerName,
    string? Classtype, string? Formula, string? ExmplAnswers, string? SurveyQuestText, string? SurveyQuestSrc, bool UnitsRequired,
    string? RelatedNames2, string? ShortName, string? OrderObs, string? Hl7FieldSubfieldId, string? ExternalCopyrightNotice,
    string? ExampleUnits, string? LongCommonName, string? ExampleUcumUnits, string? StatusReason, string? StatusText,
    string? ChangeReasonPublic, int? CommonTestRank, int? CommonOrderRank, string? Hl7AttachmentStructure,
    string? ExternalCopyrightLink, string? PanelType, string? AskAtOrderEntry, string? AssociatedObservations,
    string? VersionFirstReleased, bool ValidHl7AttachmentRequest, string? DisplayName
);
internal sealed class GetLoincCodeByIdAuth : IAuth<GetLoincCodeByIdQuery, LoincCodeDetailsResponse>
{
    public GetLoincCodeByIdAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class GetLoincCodeByIdHandler : IRequestHandler<GetLoincCodeByIdQuery, LoincCodeDetailsResponse>
{
    private readonly IDocumentStore _store;
    public GetLoincCodeByIdHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<LoincCodeDetailsResponse> Handle(GetLoincCodeByIdQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        Domain.LoincCode loincCode = await session.LoadAsync<Domain.LoincCode>(query.id);

        return new LoincCodeDetailsResponse(loincCode.Id, loincCode.LoincNum, loincCode.Component, loincCode.Property, loincCode.TimeAspct, loincCode.System, loincCode.ScaleTyp,
            loincCode.MethodTyp, loincCode.Class, loincCode.VersionLastChanged, loincCode.ChngType, loincCode.DefinitionDescription,
            loincCode.Status, loincCode.ConsumerName, loincCode.Classtype, loincCode.Formula,
            loincCode.ExmplAnswers, loincCode.SurveyQuestText, loincCode.SurveyQuestSrc, loincCode.UnitsRequired, loincCode.RelatedNames2,
            loincCode.ShortName, loincCode.OrderObs, loincCode.Hl7FieldSubfieldId, loincCode.ExternalCopyrightNotice, loincCode.ExampleUnits,
            loincCode.LongCommonName, loincCode.ExampleUcumUnits, loincCode.StatusReason, loincCode.StatusText, loincCode.ChangeReasonPublic,
            loincCode.CommonTestRank, loincCode.CommonOrderRank, loincCode.Hl7AttachmentStructure, loincCode.ExternalCopyrightLink,
            loincCode.PanelType, loincCode.AskAtOrderEntry, loincCode.AssociatedObservations, loincCode.VersionFirstReleased,
            loincCode.ValidHl7AttachmentRequest, loincCode.DisplayName);
    }
}