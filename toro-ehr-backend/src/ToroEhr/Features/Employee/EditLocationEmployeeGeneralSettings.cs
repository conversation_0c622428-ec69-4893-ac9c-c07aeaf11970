using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Employee;

public sealed record EditLocationEmployeeGeneralSettingsCommand(
    string LocationId,
    string EmployeeId,
    EditGeneralSettingsRequest EditGeneralSettingsRequest
    ) : AuthRequest<Unit>;

public sealed record EditGeneralSettingsRequest(
        string CalendarColor,
    int NumberOfAppointmentOverlaps,
    bool PendingApprovalAppointments,
    int AppointmentDurationInMinutes,
    List<string> ReceivedNotificationPreferences
    );


internal sealed class EditLocationEmployeeGeneralSettingsCommandValidator : AbstractValidator<EditLocationEmployeeGeneralSettingsCommand>
{
    public EditLocationEmployeeGeneralSettingsCommandValidator()
    {
        RuleFor(x => x.LocationId).NotEmpty();
        RuleFor(x => x.EmployeeId).NotEmpty();
        RuleFor(x => x.EditGeneralSettingsRequest.CalendarColor).NotEmpty();
        RuleFor(x => x.EditGeneralSettingsRequest.NumberOfAppointmentOverlaps).NotEmpty();
        RuleFor(x => x.EditGeneralSettingsRequest.ReceivedNotificationPreferences).NotEmpty();
        RuleForEach(x => x.EditGeneralSettingsRequest.ReceivedNotificationPreferences)
            .Must(x => ReceiveNotificationPreferencesType.TryFromName(x, out _))
            .WithMessage(x => $"ReceiveNotificationPreferencesType '{x}' is not provided");
    }
}

internal sealed class EditLocationEmployeeGeneralSettingsAuth : IAuth<EditLocationEmployeeGeneralSettingsCommand, Unit>
{
    public EditLocationEmployeeGeneralSettingsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.Practitioner]);
    }
}

internal sealed class EditLocationEmployeeGeneralSettingsHandler : IRequestHandler<EditLocationEmployeeGeneralSettingsCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditLocationEmployeeGeneralSettingsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditLocationEmployeeGeneralSettingsCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var locationEmployee = await session
            .Query<Domain.LocationEmployee>()
            .Where(x => x.LocationId == request.LocationId && x.EmployeeId == request.EmployeeId)
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(locationEmployee,
            new("LocationEmployee.NotFound", $"Employee with 'id:' {request.EmployeeId} and " +
            $"Location 'id:' {request.LocationId} not found!"));

        locationEmployee.UpdateGeneralSettings(request.EditGeneralSettingsRequest.CalendarColor, 
            request.EditGeneralSettingsRequest.NumberOfAppointmentOverlaps, request.EditGeneralSettingsRequest.AppointmentDurationInMinutes,
            request.EditGeneralSettingsRequest.PendingApprovalAppointments, request.EditGeneralSettingsRequest.ReceivedNotificationPreferences
            );

        await session.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }
}