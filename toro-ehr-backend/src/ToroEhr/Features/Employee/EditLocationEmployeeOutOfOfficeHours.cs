using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Employee;

public sealed record EditLocationEmployeeOutOfOfficeHoursCommand(
    string LocationId,
    string EmployeeId,
    EditOutOfOfficeHoursRequest EditOutOfOfficeHoursRequest
    ) : AuthRequest<Unit>;

public sealed record EditOutOfOfficeHoursRequest(
    List<OutOfOfficeHours> OutOfOfficeHoursList
    );

internal sealed class EditLocationEmployeeOutOfOfficeHoursCommandValidator : AbstractValidator<EditLocationEmployeeOutOfOfficeHoursCommand>
{
    public EditLocationEmployeeOutOfOfficeHoursCommandValidator()
    {
        RuleFor(x => x.LocationId).NotEmpty();
        RuleFor(x => x.EmployeeId).NotEmpty();
    }
}

internal sealed class EditLocationEmployeeOutOfOfficeHoursAuth : IAuth<EditLocationEmployeeOutOfOfficeHoursCommand, Unit>
{
    public EditLocationEmployeeOutOfOfficeHoursAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.Practitioner]);
    }
}

internal sealed class EditLocationEmployeeOutOfOfficeHoursHandler : IRequestHandler<EditLocationEmployeeOutOfOfficeHoursCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditLocationEmployeeOutOfOfficeHoursHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditLocationEmployeeOutOfOfficeHoursCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var locationEmployee = await session
            .Query<Domain.LocationEmployee>()
            .Where(x => x.LocationId == request.LocationId && x.EmployeeId == request.EmployeeId)
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(locationEmployee,
            new("LocationEmployee.NotFound", $"Employee with 'id:' {request.EmployeeId} and " +
            $"Location 'id:' {request.LocationId} not found!"));

        locationEmployee.UpdateOutOfOfficeHours(request.EditOutOfOfficeHoursRequest.OutOfOfficeHoursList);

        await session.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }
}