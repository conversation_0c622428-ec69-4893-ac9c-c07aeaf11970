using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Features.Shared;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Employee;

public sealed record GetListEmployeesQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<EmployeeResponse>>;

public sealed record EmployeeResponse(string Id, string Email, string FirstName, string LastName, IEnumerable<string> Roles,
    string TimeZone, string Npi, string PhoneNumber, AddressResponse? Address, DateTime? LastSeen);

internal sealed class ListEmployeesAuth : IAuth<GetListEmployeesQuery, PaginatedList<EmployeeResponse>>
{
    public ListEmployeesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin, EmployeeRole.LocationAdmin]);
    }
}

internal sealed class
    ListEmployeesHandler : IRequestHandler<GetListEmployeesQuery, PaginatedList<EmployeeResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListEmployeesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<EmployeeResponse>> Handle(GetListEmployeesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Employees_ByLocation.Entry> entries = session
            .Query<Employees_ByLocation.Entry, Employees_ByLocation>()
            .Where(x => x.LocationId == _user.SelectedLocationId)
            .ProjectInto<Employees_ByLocation.Entry>()
            .Include(x => x.EmployeeId);


        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            entries = entries.Search(x => x.SearchParams, $"{query.PagedSearchParams.SearchParam}*");
        }

        var results = await entries
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        var items = new List<EmployeeResponse>();
        foreach (var result in results) 
        {
            var employee = await session.LoadAsync<Domain.Employee>(result.EmployeeId);

            items.Add(new EmployeeResponse(
                employee.Id, employee.Email, employee.FirstName, 
                employee.LastName, result.Roles, result.TimeZone, 
                employee.Npi, employee.PhoneNumber, employee.Address != null ? new AddressResponse(
                    employee.Address.Street, employee.Address.City, 
                    employee.Address.State, employee.Address.ZipCode) : null, null));
        }

        return PaginatedList<EmployeeResponse>.Create(
            items, stats.TotalResults, query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}