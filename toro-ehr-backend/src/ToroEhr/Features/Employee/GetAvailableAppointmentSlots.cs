using MediatR;
using NodaTime;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Employee;

public sealed record GetAvailableAppointmentSlotsQuery(string EmployeeId, string LocationId, DateTimeOffset Date) : AuthRequest<List<AppointmentSlot>>;

public sealed record AppointmentSlot(DateTimeOffset From, DateTimeOffset To, int DurationInMinutes, string PractitionerTimeZone);

internal sealed class GetAvailableAppointmentSlotsAuth : IAuth<GetAvailableAppointmentSlotsQuery, List<AppointmentSlot>>
{
    public GetAvailableAppointmentSlotsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    GetAvailableAppointmentSlotsHandler : IRequestHandler<GetAvailableAppointmentSlotsQuery, List<AppointmentSlot>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetAvailableAppointmentSlotsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<AppointmentSlot>> Handle(GetAvailableAppointmentSlotsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var organizationEmployee = await session
            .Query<Domain.LocationEmployee>()
            .Where(x => x.LocationId == query.LocationId && x.EmployeeId == query.EmployeeId)
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(organizationEmployee,
            new("OrganizationEmployee.NotFound", $"Employee with 'id:' {query.EmployeeId} and " +
            $"Location 'id:' {query.LocationId} not found!"));

        var officeHours = organizationEmployee.OfficeHours.FirstOrDefault(x => x.Day == query.Date.DayOfWeek);
        if (officeHours == null || officeHours.OpenTime == null || officeHours.CloseTime == null) 
        {
            return new List<AppointmentSlot>();
        }

        var appointments = await session
            .Query<Domain.Appointment>()
            .Where(x => x.EmployeeId == query.EmployeeId && x.StartAt > query.Date.Date && x.EndAt < query.Date.AddDays(1).Date)
            .ToListAsync(cancellationToken);

        return CalculateAvailableTimeSlots(query.Date, officeHours, organizationEmployee.OutOfOfficeHours, appointments,
            organizationEmployee.NumberOfAppointmentOverlaps, organizationEmployee.AppointmentDurationInMinutes, organizationEmployee.TimeZone);
    }

    private static List<AppointmentSlot> CalculateAvailableTimeSlots(
        DateTimeOffset date,
        OfficeHours officeHours,
        List<OutOfOfficeHours> outOfOfficeHours,
        List<Domain.Appointment> bookedAppointments,
        int maxOverlappingAppointments, 
        int durationInMinutes,
        string timeZoneId)
    {
        DateTimeOffset locationDate = date.Date;
        var tzProvider = DateTimeZoneProviders.Tzdb;
        var timeZone = tzProvider[timeZoneId];


        if (timeZone != null)
        {
            var now = SystemClock.Instance.GetCurrentInstant();
            var zoned = now.InZone(timeZone);
            var offset = zoned.Offset;

            locationDate = new DateTimeOffset(date.Date, offset.ToTimeSpan());
        }

        var availableSlots = new List<AppointmentSlot>();
        var slotDuration = TimeSpan.FromMinutes(durationInMinutes);
        DateTimeOffset currentSlotStart = locationDate.Add(officeHours.OpenTime!.Value);
        DateTimeOffset endOfDay = locationDate.Add(officeHours.CloseTime!.Value);

        while (currentSlotStart < endOfDay)
        {
            DateTimeOffset slotEnd = currentSlotStart + slotDuration;

            // Check if the slot overlaps an out of office hours
            var overlappingOutOfOffice = outOfOfficeHours
                .FirstOrDefault(x => IsOverlapping(currentSlotStart, slotEnd, x.StartAt, x.EndAt));
            if (overlappingOutOfOffice != default)
            {
                // Skip to end of out of office hours period and continue
                currentSlotStart = overlappingOutOfOffice.EndAt;
                continue;
            }

            // Check if the slot overlaps an exclusion period
            var overlappingExclusion = officeHours.Exclusions
                .FirstOrDefault(e => IsOverlapping(currentSlotStart, slotEnd, locationDate.Add(e.From), locationDate.Add(e.To)));
            if (overlappingExclusion != default)
            {
                // Skip to end of exclusion period and continue
                currentSlotStart = locationDate.Add(overlappingExclusion.To);
                continue;
            }

            // Count overlapping booked appointments
            int overlappingCount = bookedAppointments.Count(b => IsOverlapping(currentSlotStart, slotEnd, b.StartAt, b.EndAt));

            // If the slot has fewer overlaps than allowed, add to available slots
            if (overlappingCount <= maxOverlappingAppointments)
            {
                availableSlots.Add(new AppointmentSlot(currentSlotStart, slotEnd, durationInMinutes, timeZoneId));
            }

            // Move to next slot dynamically
            currentSlotStart = slotEnd;
        }

        return availableSlots;
    }

    private static bool IsOverlapping(DateTimeOffset start1, DateTimeOffset end1, DateTimeOffset start2, DateTimeOffset end2)
    {
        return start1 < end2 && end1 > start2;
    }
}
