using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using ToroEhr.Enums;

namespace ToroEhr.Features.Employee;

public sealed record GetListPractitionersLookupQuery()
    : AuthRequest<List<SelectListItem>>;

internal sealed class ListPractitionersLookupAuth : IAuth<GetListPractitionersLookupQuery, List<SelectListItem>>
{
    public ListPractitionersLookupAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListPractitionersLookupHandler : IRequestHandler<GetListPractitionersLookupQuery, List<SelectListItem>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPractitionersLookupHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<SelectListItem>> Handle(GetListPractitionersLookupQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        var employeeEntries = await session
            .Query<Employees_ByLocation.Entry, Employees_ByLocation>()
            .Where(x => x.LocationId == _user.SelectedLocationId && x.Roles.Contains(EmployeeRole.Practitioner.Name))
            .ProjectInto<Employees_ByLocation.Entry>()
            .Include(x => x.EmployeeId)
            .ToListAsync(cancellationToken);

        var items = new List<SelectListItem>();
        foreach (var employeeEntrie in employeeEntries)
        {
            var employee = await session.LoadAsync<Domain.Employee>(employeeEntrie.EmployeeId);

            items.Add(new SelectListItem(employee.ShortName, employee.Id));
        }

        return items;
    }
}
