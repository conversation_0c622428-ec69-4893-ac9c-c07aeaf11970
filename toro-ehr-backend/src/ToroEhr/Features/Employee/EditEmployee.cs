using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Employee;

public sealed record EditEmployeeCommand(
    string Id,
    string OrganizationId,
    List<string> Roles,
    string Email,
    string FirstName,
    string LastName,
    string Npi,
    string PhoneNumber,
    string TimeZone,
    string CalendarColor,
    AddressRequest Address) : AuthRequest<string>;

internal sealed class EditEmployeeCommandValidator : AbstractValidator<EditEmployeeCommand>
{
    public EditEmployeeCommandValidator()
    {
        RuleFor(x => x.OrganizationId).NotEmpty();
        RuleFor(x => x.Roles).NotEmpty();
        RuleFor(x => x.Email).NotEmpty();
        RuleFor(x => x.FirstName).NotEmpty();
        RuleFor(x => x.LastName).NotEmpty();
        RuleFor(x => x.Npi).NotEmpty();
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.TimeZone).NotEmpty();
        RuleFor(x => x.CalendarColor).NotEmpty();
        RuleFor(x => x.Address).SetValidator(new AddressRequestValidator());
    }
}

internal sealed class EditEmployeeAuth : IAuth<EditEmployeeCommand, string>
{
    public EditEmployeeAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.OrganizationAdmin, EmployeeRole.LocationAdmin]);
    }
}

internal sealed class EditEmployeeHandler : IRequestHandler<EditEmployeeCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;


    public EditEmployeeHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(EditEmployeeCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.LocationEmployee locationEmployee = await session.Query<Domain.LocationEmployee>()
            .Include(x => x.EmployeeId)
            .Where(x => x.EmployeeId == request.Id && x.LocationId == _user.SelectedLocationId)
            .FirstOrDefaultAsync(cancellationToken);
        Guard.AgainstNotFound(locationEmployee, new("LocationEmployee.NotFound", $"LocationEmployee with 'id:' {request.Id} not found!"));

        Domain.Employee employee = await session.LoadAsync<Domain.Employee>(request.Id);
        Guard.AgainstNotFound(employee, new("Employee.NotFound", $"Employee with 'id:' {request.Id} not found!"));


        Domain.Address address = Domain.Address.Create(request.Address.Street, request.Address.City,
            request.Address.State, request.Address.ZipCode);

        employee.Update(request.Email, request.FirstName, request.LastName, request.Npi, request.PhoneNumber, address);
        locationEmployee.UpdateTimeZone(request.TimeZone);


        await session.SaveChangesAsync(cancellationToken);
        return employee.Id;
    }
}