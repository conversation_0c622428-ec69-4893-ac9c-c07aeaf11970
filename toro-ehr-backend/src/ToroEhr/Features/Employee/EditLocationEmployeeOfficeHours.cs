using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.Employee;

public sealed record EditLocationEmployeeOfficeHoursCommand(
    string LocationId,
    string EmployeeId,
    EditOfficeHoursRequest EditOfficeHoursRequest
    ) : AuthRequest<Unit>;

public sealed record EditOfficeHoursRequest(
    List<OfficeHours> OfficeHours
    );


internal sealed class EditLocationEmployeeOfficeHoursCommandValidator : AbstractValidator<EditLocationEmployeeOfficeHoursCommand>
{
    public EditLocationEmployeeOfficeHoursCommandValidator()
    {
        RuleFor(x => x.LocationId).NotEmpty();
        RuleFor(x => x.EmployeeId).NotEmpty();
        RuleFor(x => x.EditOfficeHoursRequest.OfficeHours).NotEmpty();
    }
}

internal sealed class EditLocationEmployeeOfficeHoursAuth : IAuth<EditLocationEmployeeOfficeHoursCommand, Unit>
{
    public EditLocationEmployeeOfficeHoursAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmRole(user, [EmployeeRole.Practitioner]);
    }
}

internal sealed class EditLocationEmployeeOfficeHoursHandler : IRequestHandler<EditLocationEmployeeOfficeHoursCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditLocationEmployeeOfficeHoursHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditLocationEmployeeOfficeHoursCommand request, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var locationEmployee = await session
            .Query<Domain.LocationEmployee>()
            .Where(x => x.LocationId == request.LocationId && x.EmployeeId == request.EmployeeId)
            .FirstOrDefaultAsync(cancellationToken);

        Guard.AgainstNotFound(locationEmployee,
            new("LocationEmployee.NotFound", $"Employee with 'id:' {request.EmployeeId} and " +
            $"Location 'id:' {request.LocationId} not found!"));

        locationEmployee.UpdateOfficeHours(request.EditOfficeHoursRequest.OfficeHours);

        await session.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }
}