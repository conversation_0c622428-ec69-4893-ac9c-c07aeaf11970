using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;

namespace ToroEhr.Features.SnomedCode;

public sealed record ImportSnomedCodesCommand(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportSnomedCodesCommandValidator : AbstractValidator<ImportSnomedCodesCommand>
{
    public ImportSnomedCodesCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportSnomedCodesAuth : IAuth<ImportSnomedCodesCommand, Unit>
{
    public ImportSnomedCodesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportSnomedCodesHandler : IRequestHandler<ImportSnomedCodesCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportSnomedCodesHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportSnomedCodesCommand request, CancellationToken cancellationToken)
    {
        var snomedCodes = await CodeService.ParseSctDescription(request.File);

        using var bulkInsert = _store.BulkInsert();

        foreach (var snomedCode in snomedCodes)
        {
            await bulkInsert.StoreAsync(snomedCode);
        }

        return Unit.Value;
    }
}