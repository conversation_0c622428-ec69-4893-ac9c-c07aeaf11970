using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;

namespace ToroEhr.Features.SnomedCode;

public sealed record GetSnomedCodeListQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<CodingResponse>>;

internal sealed class GetSnomedCodeListAuth : IAuth<GetSnomedCodeListQuery, PaginatedList<CodingResponse>>
{
    public GetSnomedCodeListAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListLocationsHandler : IRequestHandler<GetSnomedCodeListQuery, PaginatedList<CodingResponse>>
{
    private readonly IDocumentStore _store;
    public ListLocationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<PaginatedList<CodingResponse>> Handle(GetSnomedCodeListQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        IRavenQueryable<Domain.SnomedCode> dbQuery = session.Query<Domain.SnomedCode>();

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            dbQuery = dbQuery
                .Search(x => x.SnomedId, $"{query.PagedSearchParams.SearchParam}*")
                .Search(x => x.Term, $"{query.PagedSearchParams.SearchParam}*");
        }

        var medications = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        return PaginatedList<CodingResponse>.Create(
            medications.Select(x => new CodingResponse(x.Id, x.SnomedId, string.Empty, "SNOMED",
            string.Empty, x.Term)).ToList(), stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}