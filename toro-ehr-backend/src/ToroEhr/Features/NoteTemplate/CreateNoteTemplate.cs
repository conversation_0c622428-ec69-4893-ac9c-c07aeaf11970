using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.ValueObjects;

namespace ToroEhr.Features.NoteTemplate;

public sealed record CreateNoteTemplateCommand(
    string Name,
    string Classification,
    string? Specialization,
    string SpecialityCode,
    string DocumentType,
    List<NoteTemplateFieldRequest> Fields,
    List<string> LocationIds) : AuthRequest<string>;

public record NoteTemplateFieldRequest(string Name, string Value, bool IsRequired);

internal sealed class CreateNoteTemplateAuth : IAuth<CreateNoteTemplateCommand, string>
{
    public CreateNoteTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

public sealed class CreateNoteTemplateCommandValidator : AbstractValidator<CreateNoteTemplateCommand>
{
    public CreateNoteTemplateCommandValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
        RuleFor(x => x.Classification).NotEmpty();
        RuleFor(x => x.SpecialityCode).NotEmpty();
        RuleFor(x => x.DocumentType).NotEmpty();
        RuleFor(x => x.Fields).NotEmpty().WithMessage("At least one field is required.");
    }
}

internal sealed class CreateNoteTemplateHandler : IRequestHandler<CreateNoteTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateNoteTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateNoteTemplateCommand command, CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        List<NamedEntity> locations = [];
        var organization = new NamedEntity(_user.SelectedOrganizationId!, _user.OrganizationName!);
        
        if (_user.SelectedUserRole == UserRole.Employee.Name &&
            _user.SelectedLocationEmployeeRoles!.Contains(EmployeeRole.Practitioner.Name))
        {
            locations.Add(new NamedEntity(_user.SelectedLocationId!, _user.LocationName!));
        }
        else
        {
            var matchedLocations =
                (await session.LoadAsync<Domain.Location>(command.LocationIds, cancellationToken)).Values;
            locations.AddRange(matchedLocations.Select(x => new NamedEntity(x.Id, x.Name)).ToList());
        }

        var noteTemplate = Domain.NoteTemplate.Create(command.Name, command.Classification,
            command.Specialization, command.SpecialityCode, command.DocumentType,
            command.Fields.Select(x => new NoteTemplateField(x.Name, x.Value, x.IsRequired)).ToList(), organization,
            locations, new NamedEntity(_user.EmployeeId!, _user.FullName));

        await session.StoreAsync(noteTemplate, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return noteTemplate.Id;
    }
}