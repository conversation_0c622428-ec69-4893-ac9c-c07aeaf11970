using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure.ErrorHandling;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.NoteTemplate;

public sealed record GetNoteTemplateQuery(string Id) : AuthRequest<NoteTemplateDetailsResponse>;

public sealed record NoteTemplateDetailsResponse(
    string Id,
    string Name,
    string Classification,
    string? Specialization,
    string DocumentType,
    List<NoteTemplateFieldResponse> Fields,
    NamedEntityResponse organization,
    List<NamedEntityResponse> locations);

public sealed record NoteTemplateFieldResponse(string Name, string Value, bool IsRequired);

internal sealed class GetNoteTemplateAuth : IAuth<GetNoteTemplateQuery, NoteTemplateDetailsResponse>
{
    public GetNoteTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class GetNoteTemplateHandler : IRequestHandler<GetNoteTemplateQuery, NoteTemplateDetailsResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetNoteTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<NoteTemplateDetailsResponse> Handle(GetNoteTemplateQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.NoteTemplate template = await session.LoadAsync<Domain.NoteTemplate>(query.Id, cancellationToken);
        
        Guard.AgainstNotFound(template,
            new AppError("NoteTemplate.NotFound", $"Note Template with 'id:' {query.Id} not found!"));

        return new NoteTemplateDetailsResponse(
            template.Id,
            template.Name,
            template.Classification,
            template.Specialization,
            template.DocumentType,
            template.Fields.Select(f => new NoteTemplateFieldResponse(f.Name, f.Value, f.IsRequired)).ToList(),
            new NamedEntityResponse(template.Organization.Id, template.Organization.Name),
            template.Locations.Select(l => new NamedEntityResponse(l.Id, l.Name)).ToList()
        );
    }
}