using ClosedXML.Excel;
using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Immunization;

public sealed record ImportImmunizationsCommand(IFormFile File) : AuthRequest<Unit>;

internal sealed class ImportImmunizationsCommandValidator : AbstractValidator<ImportImmunizationsCommand>
{
    public ImportImmunizationsCommandValidator()
    {
        RuleFor(x => x.File).NotEmpty();
    }
}

internal sealed class ImportImmunizationsAuth : IAuth<ImportImmunizationsCommand, Unit>
{
    public ImportImmunizationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsSuperAdmin(user);
    }
}

internal sealed class ImportImmunizationsHandler : IRequestHandler<ImportImmunizationsCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ImportImmunizationsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ImportImmunizationsCommand request, CancellationToken cancellationToken)
    {
        var immunizations = await ParseImmunizationsFile(request.File);

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        foreach (var immunizationRow in immunizations)
        {
            var immunization = Domain.Immunization.Create(immunizationRow.Code, immunizationRow.CodeSystem, immunizationRow.CodeSystemName,
                immunizationRow.CodeSystemVersion, immunizationRow.DisplayName);
            await session.StoreAsync(immunization, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }

    private async Task<List<CodingRequest>> ParseImmunizationsFile(IFormFile file)
    {
        var records = new List<CodingRequest>();
        var sheetName = "Expansion List";

        using (var stream = new MemoryStream())
        {
            await file.CopyToAsync(stream);
            stream.Position = 0; // Reset stream position

            using (var workbook = new XLWorkbook(stream))
            {
                var worksheet = workbook.Worksheet(sheetName);
                if (worksheet == null)
                {
                    throw new Exception($"Sheet '{sheetName}' not found in the Excel file.");
                }

                int startRow = 15; // Start reading from row 15

                foreach (var row in worksheet.RowsUsed().Skip(startRow - 1))
                {
                    var data = new CodingRequest
                    {
                        Code = row.Cell(1).GetValue<string>(),  // Column A
                        DisplayName = row.Cell(2).GetValue<string>(),     // Column B
                        CodeSystemName = row.Cell(3).GetValue<string>(), // Column C
                        CodeSystemVersion = row.Cell(4).GetValue<string>(), // Column D
                        CodeSystem = row.Cell(4).GetValue<string>(), // Column E
                    };

                    records.Add(data);
                }
            }
        }

        return records;
    }
}