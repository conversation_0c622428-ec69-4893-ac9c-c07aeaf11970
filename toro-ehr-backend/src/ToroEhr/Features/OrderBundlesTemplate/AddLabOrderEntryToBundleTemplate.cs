using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record AddLabOrderEntryToBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string LoincCodeId, string Specimen, 
    string Note, bool Fasting, bool Repeat, bool IsRequired) 
    : AuthRequest<string>;

internal class AddLabOrderEntryToBundleTemplateAuth : IAuth<AddLabOrderEntryToBundleTemplateCommand, string>
{
    public AddLabOrderEntryToBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class AddLabOrderEntryToBundleTemplateValidator : AbstractValidator<AddLabOrderEntryToBundleTemplateCommand>
{
    public AddLabOrderEntryToBundleTemplateValidator()
    {
        RuleFor(x => x.LoincCodeId).NotEmpty();
        RuleFor(x => x.Specimen).NotEmpty();
    }
}

internal class AddLabOrderEntryToBundleTemplateHandler : IRequestHandler<AddLabOrderEntryToBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AddLabOrderEntryToBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(AddLabOrderEntryToBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.LoincCode loincCode = await session.LoadAsync<Domain.LoincCode>(command.LoincCodeId);
        Guard.AgainstNotFound(loincCode, new("LoincCode.NotFound", $"Loinc Code with 'id:' {command.LoincCodeId} not found!"));

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        LabOrderEntry labOrderEntry = LabOrderEntry.Create(command.LoincCodeId, command.Specimen, command.Note,
            loincCode.DisplayName ?? string.Empty, command.Fasting, command.Repeat, command.IsRequired);

        OrderBundleTemplateStep? step = orderBundleTemplate.Steps.FirstOrDefault(x => x.StepNumber == command.StepNumber);
        if (step == null)
        {
            step = OrderBundleTemplateStep.Create(command.StepNumber);
            orderBundleTemplate.Steps.Add(step);
        }

        step.OrderEntries.Add(labOrderEntry);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return labOrderEntry.Id;
    }
}