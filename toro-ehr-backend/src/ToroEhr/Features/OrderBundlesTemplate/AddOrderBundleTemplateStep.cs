using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record AddOrderBundleTemplateStepCommand(string OrderBundleTemplateId, int StepNumber) : AuthRequest<string>;

internal class AddOrderBundleTemplateStepAuth : IAuth<AddOrderBundleTemplateStepCommand, string>
{
    public AddOrderBundleTemplateStepAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class AddOrderBundleTemplateStepValidator : AbstractValidator<AddOrderBundleTemplateStepCommand>
{
    public AddOrderBundleTemplateStepValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
    }
}

internal class AddOrderBundleTemplateStepHandler : IRequestHandler<AddOrderBundleTemplateStepCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AddOrderBundleTemplateStepHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(AddOrderBundleTemplateStepCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));
        AgainstAlreadyCreatedStep(orderBundleTemplate, command.StepNumber);

        orderBundleTemplate.Steps.Add(OrderBundleTemplateStep.Create(command.StepNumber));

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundleTemplate.Id;
    }

    public static void AgainstAlreadyCreatedStep(OrderBundleTemplate orderBundlesTemplate, int stepNumber)
    {
        if (orderBundlesTemplate.Steps.Any(s => s.StepNumber == stepNumber))
            throw new ValidationException(
                [new("OrderBundleTemplateStep.AlreadyExist", $"OrderBundleTemplateStep with 'stepNumber:' {stepNumber} already exist!")]);
    }
}