using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;
using ToroEhr.Features.Encounter.Shared;
using System.Text.Json.Serialization;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record GetOrderBundleTemplateQuery(string Id) : AuthRequest<OrderBundleTemplateResponse>;

internal class GetOrderBundleTemplateAuth : IAuth<GetOrderBundleTemplateQuery, OrderBundleTemplateResponse>
{
    public GetOrderBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public record OrderBundleTemplateResponse(string LocationId, string TemplateName, string Priority, 
    DateTimeOffset CreatedAt, string CreatedBy, List<OrderBundleTemplateStepResponse> Steps);

public record OrderBundleTemplateStepResponse(int StepNumber, List<OrderTemplateEntryResponse> OrderEntries);

[JsonPolymorphic(TypeDiscriminatorPropertyName = "orderType")]
[JsonDerivedType(typeof(MedicationTemplateEntryResponse), "Med")]
[JsonDerivedType(typeof(LabTemplateEntryResponse), "Lab")]
[JsonDerivedType(typeof(ProcedureTemplateEntryResponse), "Procedure")]
[JsonDerivedType(typeof(BundleTemplateEntryResponse), "Bundle")]
public abstract record OrderTemplateEntryResponse(string Id, string Name, bool IsRequired, SearchOrderEntryResponse OrderEntry);
public record MedicationTemplateEntryResponse(string Id, string MedicationId, string IngredientId, string Frequency, string? CustomFrequency, string Duration,
    bool Prn, string? PrnReason, string Name, bool IsRequired, SearchOrderEntryResponse OrderEntry) : OrderTemplateEntryResponse(Id, Name, IsRequired, OrderEntry);
public record LabTemplateEntryResponse(string Id, string LoincCodeId, string Specimen, string Note, string Name, bool Fasting, bool Repeat, bool IsRequired, SearchOrderEntryResponse OrderEntry) : OrderTemplateEntryResponse(Id, Name, IsRequired, OrderEntry);
public record ProcedureTemplateEntryResponse(string Id, string LoincCodeId, string Note, string Name, bool Fasting, bool Repeat, bool IsRequired, SearchOrderEntryResponse OrderEntry) : OrderTemplateEntryResponse(Id, Name, IsRequired, OrderEntry);
public record BundleTemplateEntryResponse(string Id, string BundleId, List<OrderTemplateEntryResponse> NestedOrderEntries, string Name, bool IsRequired, SearchOrderEntryResponse OrderEntry) : OrderTemplateEntryResponse(Id, Name, IsRequired, OrderEntry);


internal class Handler : IRequestHandler<GetOrderBundleTemplateQuery, OrderBundleTemplateResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public Handler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<OrderBundleTemplateResponse> Handle(GetOrderBundleTemplateQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(query.Id, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {query.Id} not found!"));

        List<OrderBundleTemplateStepResponse> steps = new List<OrderBundleTemplateStepResponse>();

        foreach (var step in orderBundleTemplate.Steps)
        {
            var entries = await MapEntriesAsync(step.OrderEntries, session, cancellationToken);
            steps.Add(new OrderBundleTemplateStepResponse(step.StepNumber, entries));
        }

        return new OrderBundleTemplateResponse(
           orderBundleTemplate.Id,
           orderBundleTemplate.Name,
           orderBundleTemplate.Priority,
           orderBundleTemplate.CreatedAt,
           orderBundleTemplate.CreatedBy,
           steps
        );
    }

    private async Task<List<OrderTemplateEntryResponse>> MapEntriesAsync(
    IEnumerable<OrderEntry> entries, IAsyncDocumentSession session, CancellationToken ct)
    {
        var result = new List<OrderTemplateEntryResponse>();

        foreach (var entry in entries)
        {
            switch (entry)
            {
                case MedicationOrderEntry m:
                    var ingredient = await session.LoadAsync<Domain.Medication>(m.IngredientId, ct);
                    result.Add(new MedicationTemplateEntryResponse(
                        m.Id, m.MedicationId, m.IngredientId, m.Frequency, m.CustomFrequency, m.Duration,
                        m.Prn, m.PrnReason, m.Name, m.IsRequired,
                        new SearchOrderEntryResponse(ingredient.Id, ingredient.RxCui, ingredient.TermString, "Med")));
                    break;

                case LabOrderEntry l:
                    var loincCode = await session.LoadAsync<Domain.LoincCode>(l.LoincCodeId, ct);
                    result.Add(new LabTemplateEntryResponse(
                        l.Id, l.LoincCodeId, l.Specimen, l.Note, l.Name, l.Fasting, l.Repeat, l.IsRequired,
                        new SearchOrderEntryResponse(loincCode.Id, loincCode.LoincNum ?? string.Empty, loincCode.DisplayName ?? string.Empty, "Lab")));
                    break;

                case ProcedureOrderEntry l:
                    var snomedCode = await session.LoadAsync<Domain.SnomedCode>(l.SnomedCodeId, ct);
                    result.Add(new ProcedureTemplateEntryResponse(
                        l.Id, l.SnomedCodeId, l.Note, l.Name, l.Fasting, l.Repeat, l.IsRequired,
                        new SearchOrderEntryResponse(snomedCode.Id, snomedCode.SnomedId ?? string.Empty, snomedCode.Term ?? string.Empty, "Procedure")));
                    break;

                case BundleOrderEntry b:
                    var nestedEntries = await MapEntriesAsync(b.OrderEntries, session, ct); // 🔁 Recursive step
                    result.Add(new BundleTemplateEntryResponse(
                        b.Id, b.BundleId, nestedEntries, b.Name, b.IsRequired,
                        new SearchOrderEntryResponse(b.Id, string.Empty, b.Name, "Bundle")));
                    break;

                default:
                    throw new InvalidOperationException($"Unknown entry type: {entry.GetType()}");
            }
        }

        return result;
    }
}