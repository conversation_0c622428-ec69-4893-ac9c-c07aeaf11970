using MediatR;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record GetOrderBundleTemplateListQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<OrderBundleTemplateListResponse>>;

public record OrderBundleTemplateListResponse( string Id,
    string LocationId, string TemplateName, string Priority, DateTimeOffset CreatedAt, string CreatedBy);

internal class GetOrderBundleTemplateListAuth : IAuth<GetOrderBundleTemplateListQuery, PaginatedList<OrderBundleTemplateListResponse>>
{
    public GetOrderBundleTemplateListAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    GetOrderBundleTemplateListHandler : IRequestHandler<GetOrderBundleTemplateListQuery, PaginatedList<OrderBundleTemplateListResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetOrderBundleTemplateListHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<OrderBundleTemplateListResponse>> Handle(GetOrderBundleTemplateListQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Domain.OrderBundleTemplate> dbQuery = session.Query<Domain.OrderBundleTemplate>()
            .Include(x => x.CreatedBy)
            .Where(x => x.LocationId == _user.SelectedLocationId);


        IEnumerable<Domain.OrderBundleTemplate> orderBundleTemplates = await dbQuery
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        List<OrderBundleTemplateListResponse> orderBundleTemplateList = new List<OrderBundleTemplateListResponse>();
        foreach (var orderBundleTemplate in orderBundleTemplates)
        {
            Domain.Employee employee = await session.LoadAsync<Domain.Employee>(orderBundleTemplate.CreatedBy);
            orderBundleTemplateList.Add(new OrderBundleTemplateListResponse(
                orderBundleTemplate.Id,
                orderBundleTemplate.LocationId,
                orderBundleTemplate.Name,
                orderBundleTemplate.Priority,
                orderBundleTemplate.CreatedAt,
                employee.FullName));
        }

        return PaginatedList<OrderBundleTemplateListResponse>.Create(
            orderBundleTemplateList,
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize
        );
    }
}