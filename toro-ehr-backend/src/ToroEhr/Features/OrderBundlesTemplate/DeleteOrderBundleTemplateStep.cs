using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record DeleteOrderBundleTemplateStepCommand(string OrderBundleTemplateId, int StepNumber) : AuthRequest<Unit>;
internal class DeleteOrderBundleTemplateStepAuth : IAuth<DeleteOrderBundleTemplateStepCommand, Unit>
{
    public DeleteOrderBundleTemplateStepAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class DeleteOrderBundleTemplateStepValidator : AbstractValidator<DeleteOrderBundleTemplateStepCommand>
{
    public DeleteOrderBundleTemplateStepValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
    }
}

internal class DeleteOrderBundleTemplateStepHandler : IRequestHandler<DeleteOrderBundleTemplateStepCommand, Unit>
{
    private readonly IDocumentStore _store;

    public DeleteOrderBundleTemplateStepHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(DeleteOrderBundleTemplateStepCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        OrderBundleTemplateStep? step = orderBundleTemplate.Steps.FirstOrDefault(x => x.StepNumber == command.StepNumber);
        Guard.AgainstNotFound(step, new("OrderBundleTemplateStep.NotFound", $"OrderBundleTemplateStep with 'number:' {command.StepNumber} not found!"));

        if(orderBundleTemplate.Steps.Any(x => x.StepNumber > step.StepNumber) || step.OrderEntries.Any())
        {
            throw new ValidationException(
                [new("OrderBundleTemplateStep.CantBeDeleted", $"OrderBundleTemplateStep with 'stepNumber:' {command.StepNumber} can't be deleted!")]);
        }

        orderBundleTemplate.Steps.Remove(step);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return Unit.Value;
    }
}