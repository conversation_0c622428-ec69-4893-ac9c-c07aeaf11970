using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record EditMedicationOrderEntryInBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string OrderEntryId, string? NestedBundleId, 
    string MedicationId, string IngredientId, string Frequency, string? CustomFrequency, string Duration, bool Prn, string? PrnReason, 
    bool IsRequired) : AuthRequest<string>;

internal class EditMedicationOrderEntryInBundleTemplateAuth : IAuth<EditMedicationOrderEntryInBundleTemplateCommand, string>
{
    public EditMedicationOrderEntryInBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class EditMedicationOrderEntryInBundleTemplateValidator : AbstractValidator<EditMedicationOrderEntryInBundleTemplateCommand>
{
    public EditMedicationOrderEntryInBundleTemplateValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
        RuleFor(x => x.OrderEntryId).NotEmpty();
        RuleFor(x => x.MedicationId).NotEmpty();
        RuleFor(x => x.IngredientId).NotEmpty();
        RuleFor(x => x.Frequency).NotEmpty();
        RuleFor(x => x.Duration).NotEmpty();
    }
}

internal class EditMedicationOrderEntryInBundleTemplateHandler : IRequestHandler<EditMedicationOrderEntryInBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public EditMedicationOrderEntryInBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(EditMedicationOrderEntryInBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        var orderEntryId = command.OrderEntryId;
        if (command.NestedBundleId.IsNotNullOrWhiteSpace())
        {
            orderEntryId = command.NestedBundleId;
        }

        OrderEntry? orderEntry = orderBundleTemplate.Steps.SelectMany(x => x.OrderEntries).FirstOrDefault(x => x.Id == orderEntryId);
        Guard.AgainstNotFound(orderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {command.OrderEntryId} not found!"));

        Domain.Medication medication = await session.LoadAsync<Domain.Medication>(command.MedicationId);
        Guard.AgainstNotFound(medication, new("Medication.NotFound", $"Medication with 'id:' {command.MedicationId} not found!"));

        //todo: check step and type
        if (orderEntry is MedicationOrderEntry m)
        {
            m.Update(command.MedicationId, command.IngredientId, command.Frequency, command.CustomFrequency, command.Duration, command.Prn, command.PrnReason,
                medication.TermString, command.IsRequired);
        }
        else if (orderEntry is BundleOrderEntry b)
        {
            OrderEntry? nestedOrderEntry = b.OrderEntries.FirstOrDefault(x => x.Id == command.OrderEntryId);
            Guard.AgainstNotFound(nestedOrderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {nestedOrderEntry} not found!"));

            if (nestedOrderEntry is MedicationOrderEntry nm)
            {
                nm.Update(command.MedicationId, command.IngredientId, command.Frequency, command.CustomFrequency, command.Duration, command.Prn, command.PrnReason,
                    medication.TermString, command.IsRequired);
            }
        }

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundleTemplate.Id;
    }
}
