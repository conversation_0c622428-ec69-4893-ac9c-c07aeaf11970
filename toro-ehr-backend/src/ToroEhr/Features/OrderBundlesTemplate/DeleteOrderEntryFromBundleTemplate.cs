using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;
using ToroEhr.Shared;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record DeleteOrderEntryFromBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string OrderEntryId, string? NestedBundleId) : AuthRequest<Unit>;

internal class DeleteOrderEntryFromBundleTemplateAuth : IAuth<DeleteOrderEntryFromBundleTemplateCommand, Unit>
{
    public DeleteOrderEntryFromBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class DeleteOrderEntryFromBundleTemplateValidator : AbstractValidator<DeleteOrderEntryFromBundleTemplateCommand>
{
    public DeleteOrderEntryFromBundleTemplateValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
        RuleFor(x => x.OrderEntryId).NotEmpty();
    }
}

internal class DeleteOrderEntryFromBundleTemplateHandler : IRequestHandler<DeleteOrderEntryFromBundleTemplateCommand, Unit>
{
    private readonly IDocumentStore _store;

    public DeleteOrderEntryFromBundleTemplateHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(DeleteOrderEntryFromBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        OrderBundleTemplateStep? step = orderBundleTemplate.Steps.FirstOrDefault(x => x.StepNumber == command.StepNumber);
        Guard.AgainstNotFound(step, new("OrderBundleTemplateStep.NotFound", $"OrderBundleTemplateStep with 'number:' {command.StepNumber} not found!"));

        if (command.NestedBundleId.IsNotNullOrWhiteSpace())
        {
            OrderEntry? bundleOrderEntry = step.OrderEntries.FirstOrDefault(x => x.Id == command.NestedBundleId);
            Guard.AgainstNotFound(bundleOrderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {command.NestedBundleId} not found!"));

            if (bundleOrderEntry is BundleOrderEntry b)
            {
                OrderEntry? nestedOrderEntry = b.OrderEntries.FirstOrDefault(x => x.Id == command.OrderEntryId);
                Guard.AgainstNotFound(nestedOrderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {command.OrderEntryId} not found!"));

                b.OrderEntries.Remove(nestedOrderEntry);
            }
        }
        else
        {
            OrderEntry? orderEntry = step.OrderEntries.FirstOrDefault(x => x.Id == command.OrderEntryId);
            Guard.AgainstNotFound(orderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {command.OrderEntryId} not found!"));

            step.OrderEntries.Remove(orderEntry);
        }

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return Unit.Value;
    }
}