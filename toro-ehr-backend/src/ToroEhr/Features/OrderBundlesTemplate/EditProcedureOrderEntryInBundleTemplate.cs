using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record EditProcedureOrderEntryInBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string OrderEntryId, string? NestedBundleId,
    string SnomedCodeId, string Note, bool Fasting, bool Repeat, bool IsRequired) : AuthRequest<string>;

internal class EditProcedureOrderEntryInBundleTemplateAuth : IAuth<EditProcedureOrderEntryInBundleTemplateCommand, string>
{
    public EditProcedureOrderEntryInBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class EditProcedureOrderEntryInBundleTemplateValidator : AbstractValidator<EditProcedureOrderEntryInBundleTemplateCommand>
{
    public EditProcedureOrderEntryInBundleTemplateValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
        RuleFor(x => x.OrderEntryId).NotEmpty();
        RuleFor(x => x.SnomedCodeId).NotEmpty();
    }
}

internal class EditProcedureOrderEntryInBundleTemplateHandler : IRequestHandler<EditProcedureOrderEntryInBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public EditProcedureOrderEntryInBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(EditProcedureOrderEntryInBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        var orderEntryId = command.OrderEntryId;
        if (command.NestedBundleId.IsNotNullOrWhiteSpace())
        {
            orderEntryId = command.NestedBundleId;
        }

        OrderEntry? orderEntry = orderBundleTemplate.Steps.SelectMany(x => x.OrderEntries).FirstOrDefault(x => x.Id == orderEntryId);
        Guard.AgainstNotFound(orderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {command.OrderEntryId} not found!"));

        Domain.SnomedCode snomedCode = await session.LoadAsync<Domain.SnomedCode>(command.SnomedCodeId);
        Guard.AgainstNotFound(snomedCode, new("SnomedCode.NotFound", $"Snomed Code with 'id:' {command.SnomedCodeId} not found!"));

        //todo: check step and type
        if (orderEntry is ProcedureOrderEntry l)
        {
            l.Update(command.SnomedCodeId, command.Note, snomedCode.Term ?? string.Empty, command.Fasting, command.Repeat, command.IsRequired);
        }
        else if (orderEntry is BundleOrderEntry b)
        {
            OrderEntry? nestedOrderEntry = b.OrderEntries.FirstOrDefault(x => x.Id == command.OrderEntryId);
            Guard.AgainstNotFound(nestedOrderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {nestedOrderEntry} not found!"));

            if (nestedOrderEntry is ProcedureOrderEntry nl)
            {
                nl.Update(command.SnomedCodeId, command.Note, snomedCode.Term ?? string.Empty, command.Fasting, command.Repeat, command.IsRequired);
            }
        }

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundleTemplate.Id;
    }
}