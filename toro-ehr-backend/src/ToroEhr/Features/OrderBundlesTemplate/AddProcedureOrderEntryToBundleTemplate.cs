using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record AddProcedureOrderEntryToBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string SnomedCodeId,
    string Note, bool Fasting, bool Repeat, bool IsRequired)
    : AuthRequest<string>;

internal class AddProcedureOrderEntryToBundleTemplateAuth : IAuth<AddProcedureOrderEntryToBundleTemplateCommand, string>
{
    public AddProcedureOrderEntryToBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class AddProcedureOrderEntryToBundleTemplateValidator : AbstractValidator<AddProcedureOrderEntryToBundleTemplateCommand>
{
    public AddProcedureOrderEntryToBundleTemplateValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
        RuleFor(x => x.SnomedCodeId).NotEmpty();
    }
}

internal class AddProcedureOrderEntryToBundleTemplateHandler : IRequestHandler<AddProcedureOrderEntryToBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AddProcedureOrderEntryToBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(AddProcedureOrderEntryToBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.SnomedCode snomedCode = await session.LoadAsync<Domain.SnomedCode>(command.SnomedCodeId);
        Guard.AgainstNotFound(snomedCode, new("SnomedCode.NotFound", $"Snomed Code with 'id:' {command.SnomedCodeId} not found!"));

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        ProcedureOrderEntry procedureOrderEntry = ProcedureOrderEntry.Create(command.SnomedCodeId, command.Note,
            snomedCode.Term ?? string.Empty, command.Fasting, command.Repeat, command.IsRequired);

        OrderBundleTemplateStep? step = orderBundleTemplate.Steps.FirstOrDefault(x => x.StepNumber == command.StepNumber);
        if (step == null)
        {
            step = OrderBundleTemplateStep.Create(command.StepNumber);
            orderBundleTemplate.Steps.Add(step);
        }

        step.OrderEntries.Add(procedureOrderEntry);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return procedureOrderEntry.Id;
    }
}