using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record CreateOrderBundleTemplateCommand(string LocationId, string TemplateName, string Priority) : AuthRequest<string>;

internal class CreateOrderBundleTemplateAuth : IAuth<CreateOrderBundleTemplateCommand, string>
{
    public CreateOrderBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class CreateOrderMedicineValidator : AbstractValidator<CreateOrderBundleTemplateCommand>
{
    public CreateOrderMedicineValidator()
    {
        RuleFor(x => x.LocationId).NotEmpty();
        RuleFor(x => x.TemplateName).NotEmpty();
        RuleFor(x => x.Priority).NotEmpty();
    }
}

internal class CreateOrderBundleTemplateHandler : IRequestHandler<CreateOrderBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrderBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrderBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = OrderBundleTemplate.Create(command.LocationId, command.TemplateName, command.Priority,
            DateTimeOffset.UtcNow, _user.EmployeeId!);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundleTemplate.Id;
    }
}