using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record AddMedicationOrderEntryToBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string MedicationId, string IngredientId, string Frequency, string? CustomFrequency,
    string Duration, bool Prn, string? PrnReason, bool IsRequired) : AuthRequest<string>;

internal class AddMedicationOrderEntryToBundleTemplateAuth : IAuth<AddMedicationOrderEntryToBundleTemplateCommand, string>
{
    public AddMedicationOrderEntryToBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class AddMedicationOrderEntryToBundleTemplateValidator : AbstractValidator<AddMedicationOrderEntryToBundleTemplateCommand>
{
    public AddMedicationOrderEntryToBundleTemplateValidator()
    {
        RuleFor(x => x.MedicationId).NotEmpty();
        RuleFor(x => x.IngredientId).NotEmpty();
        RuleFor(x => x.Frequency).NotEmpty();
        RuleFor(x => x.Duration).NotEmpty();
    }
}

internal class AddMedicationOrderEntryToBundleTemplateHandler : IRequestHandler<AddMedicationOrderEntryToBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AddMedicationOrderEntryToBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(AddMedicationOrderEntryToBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Medication medication = await session.LoadAsync<Domain.Medication>(command.MedicationId);
        Guard.AgainstNotFound(medication, new("Medication.NotFound", $"Medication with 'id:' {command.MedicationId} not found!"));

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        MedicationOrderEntry medicationOrderEntry = MedicationOrderEntry.Create(command.MedicationId, command.IngredientId, command.Frequency,
            command.CustomFrequency, command.Duration, command.Prn, command.PrnReason, medication.TermString,
            command.IsRequired/*, DateTimeOffset.UtcNow, _user.EmployeeId!*/);

        OrderBundleTemplateStep? step = orderBundleTemplate.Steps.FirstOrDefault(x => x.StepNumber == command.StepNumber);
        if (step == null) 
        {
            step = OrderBundleTemplateStep.Create(command.StepNumber);
            orderBundleTemplate.Steps.Add(step);
        }

        step.OrderEntries.Add(medicationOrderEntry);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return medicationOrderEntry.Id;
    }
}