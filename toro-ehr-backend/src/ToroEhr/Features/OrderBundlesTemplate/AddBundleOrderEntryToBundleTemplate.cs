using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record AddBundleOrderEntryToBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string BundleId) : AuthRequest<string>;

internal class AddBundleOrderEntryToBundleTemplateAuth : IAuth<AddBundleOrderEntryToBundleTemplateCommand, string>
{
    public AddBundleOrderEntryToBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class AddBundleOrderEntryToBundleTemplateValidator : AbstractValidator<AddBundleOrderEntryToBundleTemplateCommand>
{
    public AddBundleOrderEntryToBundleTemplateValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.BundleId).NotEmpty();
    }
}

internal class AddBundleOrderEntryToBundleTemplateHandler : IRequestHandler<AddBundleOrderEntryToBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AddBundleOrderEntryToBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(AddBundleOrderEntryToBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        OrderBundleTemplate bundle = await session.LoadAsync<OrderBundleTemplate>(command.BundleId, cancellationToken);
        Guard.AgainstNotFound(bundle, new("OrderBundleTemplate.NotFound", $"Bundle with 'id:' {command.BundleId} not found!"));

        List<OrderEntry> orderEntries = bundle.Steps.SelectMany(x => x.OrderEntries).ToList();
        //todo: flatten inside bundles if exists

        BundleOrderEntry bundleOrderEntry = BundleOrderEntry.Create(command.BundleId, orderEntries, bundle.Name);

        OrderBundleTemplateStep? step = orderBundleTemplate.Steps.FirstOrDefault(x => x.StepNumber == command.StepNumber);
        if (step == null)
        {
            step = OrderBundleTemplateStep.Create(command.StepNumber);
            orderBundleTemplate.Steps.Add(step);
        }

        step.OrderEntries.Add(bundleOrderEntry);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return bundleOrderEntry.Id;
    }
}