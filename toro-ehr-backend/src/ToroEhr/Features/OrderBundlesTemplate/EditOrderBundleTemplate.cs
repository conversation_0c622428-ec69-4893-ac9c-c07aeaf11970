using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record EditOrderBundleTemplateCommand(string OrderBundleTemplateId, string LocationId, string TemplateName, string Priority) : AuthRequest<string>;

internal class EditOrderBundleTemplateAuth : IAuth<EditOrderBundleTemplateCommand, string>
{
    public EditOrderBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class EditOrderBundleTemplateValidator : AbstractValidator<EditOrderBundleTemplateCommand>
{
    public EditOrderBundleTemplateValidator()
    {
        RuleFor(x => x.LocationId).NotEmpty();
        RuleFor(x => x.TemplateName).NotEmpty();
        RuleFor(x => x.Priority).NotEmpty();
    }
}

internal class EditOrderBundleTemplateHandler : IRequestHandler<EditOrderBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public EditOrderBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(EditOrderBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        orderBundleTemplate.Update(command.LocationId, command.TemplateName, command.Priority);

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundleTemplate.Id;
    }
}