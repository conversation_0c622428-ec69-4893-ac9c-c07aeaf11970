using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.OrderBundlesTemplate;

public record EditLabOrderEntryInBundleTemplateCommand(string OrderBundleTemplateId, int StepNumber, string OrderEntryId, string? NestedBundleId,
    string LoincCodeId, string Specimen, string Note, bool Fasting, bool Repeat, bool IsRequired) : AuthRequest<string>;

internal class EditLabOrderEntryInBundleTemplateAuth : IAuth<EditLabOrderEntryInBundleTemplateCommand, string>
{
    public EditLabOrderEntryInBundleTemplateAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public class EditLabOrderEntryInBundleTemplateValidator : AbstractValidator<EditLabOrderEntryInBundleTemplateCommand>
{
    public EditLabOrderEntryInBundleTemplateValidator()
    {
        RuleFor(x => x.OrderBundleTemplateId).NotEmpty();
        RuleFor(x => x.StepNumber).NotEmpty();
        RuleFor(x => x.OrderEntryId).NotEmpty();
        RuleFor(x => x.LoincCodeId).NotEmpty();
        RuleFor(x => x.Specimen).NotEmpty();
    }
}

internal class EditLabOrderEntryInBundleTemplateHandler : IRequestHandler<EditLabOrderEntryInBundleTemplateCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public EditLabOrderEntryInBundleTemplateHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(EditLabOrderEntryInBundleTemplateCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate orderBundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.OrderBundleTemplateId, cancellationToken);
        Guard.AgainstNotFound(orderBundleTemplate, new("OrderBundleTemplate.NotFound", $"OrderBundleTemplate with 'id:' {command.OrderBundleTemplateId} not found!"));

        var orderEntryId = command.OrderEntryId;
        if (command.NestedBundleId.IsNotNullOrWhiteSpace())
        {
            orderEntryId = command.NestedBundleId;
        }

        OrderEntry? orderEntry = orderBundleTemplate.Steps.SelectMany(x => x.OrderEntries).FirstOrDefault(x => x.Id == orderEntryId);
        Guard.AgainstNotFound(orderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {command.OrderEntryId} not found!"));

        Domain.LoincCode loincCode = await session.LoadAsync<Domain.LoincCode>(command.LoincCodeId);
        Guard.AgainstNotFound(loincCode, new("LoincCode.NotFound", $"Loinc Code with 'id:' {command.LoincCodeId} not found!"));

        //todo: check step and type
        if (orderEntry is LabOrderEntry l)
        {
            l.Update(command.LoincCodeId, command.Specimen, command.Note, loincCode.DisplayName ?? string.Empty, command.Fasting, command.Repeat, command.IsRequired);
        }
        else if (orderEntry is BundleOrderEntry b)
        {
            OrderEntry? nestedOrderEntry = b.OrderEntries.FirstOrDefault(x => x.Id == command.OrderEntryId);
            Guard.AgainstNotFound(nestedOrderEntry, new("OrderEntry.NotFound", $"OrderEntry with 'id:' {nestedOrderEntry} not found!"));

            if (nestedOrderEntry is LabOrderEntry nl)
            {
                nl.Update(command.LoincCodeId, command.Specimen, command.Note, loincCode.DisplayName ?? string.Empty, command.Fasting, command.Repeat, command.IsRequired);
            }
        }

        await session.StoreAsync(orderBundleTemplate, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundleTemplate.Id;
    }
}