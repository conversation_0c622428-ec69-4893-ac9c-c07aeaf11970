using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Enums;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record ChangeOrderStatusCommand(List<string> OrderIds, string Status, string? BundleId) : AuthRequest<Unit>;

internal class ChangeOrderStatusAuth : IAuth<ChangeOrderStatusCommand, Unit>
{
    public ChangeOrderStatusAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class ChangeOrderStatusValidator : AbstractValidator<ChangeOrderStatusCommand>
{
    public ChangeOrderStatusValidator()
    {
        RuleFor(x => x.OrderIds).NotEmpty();
        RuleFor(x => x.Status).NotEmpty();
    }
}

internal class ChangeOrderStatusHandler : IRequestHandler<ChangeOrderStatusCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ChangeOrderStatusHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ChangeOrderStatusCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        if (command.BundleId.IsNullOrWhiteSpace())
        {
            ICollection<Order> orders = (await session.LoadAsync<Order>(command.OrderIds, cancellationToken)).Values;
            foreach (var order in orders)
            {
                if (order is OrderBundle ob)
                {
                    foreach (var bundleOrders in ob.Orders)
                    {
                        bundleOrders.UpdateStatus(command.Status);
                        order.UpdateStatus(command.Status);
                        if (command.Status == OrderStatus.Completed.Name || command.Status == OrderStatus.Revoked.Name)
                        {
                            bundleOrders.UpdateCompletedAt(DateTimeOffset.UtcNow);
                            order.UpdateCompletedAt(DateTimeOffset.UtcNow);
                        }
                    }
                }
                else
                {
                    order.UpdateStatus(command.Status);
                    if (command.Status == OrderStatus.Completed.Name || command.Status == OrderStatus.Revoked.Name)
                    {
                        order.UpdateCompletedAt(DateTimeOffset.UtcNow);
                    }
                }

            }
        } else
        {
            OrderBundle orderBundle = await session.LoadAsync<OrderBundle>(command.BundleId, cancellationToken);
            List<Order> ordersForEdit = orderBundle.Orders.Where(x => command.OrderIds.Contains(x.Id)).ToList();
            foreach (var order in ordersForEdit)
            {
                order.UpdateStatus(command.Status);
                if (command.Status == OrderStatus.Completed.Name || command.Status == OrderStatus.Revoked.Name)
                {
                    order.UpdateCompletedAt(DateTimeOffset.UtcNow);
                }
            }
            if (orderBundle.Orders.All(x => x.Status == OrderStatus.Completed.Name || x.Status == OrderStatus.Revoked.Name))
            {
                orderBundle.UpdateStatus(OrderStatus.Completed.Name);
                orderBundle.UpdateCompletedAt(DateTimeOffset.UtcNow);
            }
        }


        await session.SaveChangesAsync();

        return Unit.Value;
    }
}