using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record AddVitalSignsCommand(string PatientId, string EncounterId, List<VitalSignRequest> Vitals)
    : AuthRequest<Unit>;

public sealed record VitalSignRequest(string Value, string Type);

internal sealed class AddVitalSignsCommandAuth : IAuth<AddVitalSignsCommand, Unit>
{
    public AddVitalSignsCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class AddVitalSignsCommandValidator : AbstractValidator<AddVitalSignsCommand>
{
    public AddVitalSignsCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal sealed class AddVitalSignsHandler : IRequestHandler<AddVitalSignsCommand, Unit>
{
    private readonly IDocumentStore _store;

    public AddVitalSignsHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(AddVitalSignsCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();
        
        Domain.Encounter encounter = await session.LoadAsync<Domain.Encounter>(command.EncounterId, cancellationToken);
        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(command.PatientId, cancellationToken);

        Guard.AgainstNotFound(encounter, EncounterErrors.NotFoundById(patient.Id));
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(patient.Id));

        VitalSign vitalSign = VitalSign.Create(
            command.PatientId,
            command.EncounterId,
            DateTimeOffset.UtcNow,
            command.Vitals.Select(x => new Measurement(x.Type, x.Value)).ToList());

        await session.StoreAsync(vitalSign, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}