using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Services;
using ToroEhr.Infrastructure;
using ToroEhr.ValueObjects;
using ToroEhr.Infrastructure.Guards;

namespace ToroEhr.Features.Encounter;

public sealed record ReceiveEncounterEmailCommand(string EncounterEmail, string Subject, string Body, IFormFileCollection Attachments)
    : IRequest<Unit>;

internal sealed class ReceiveEncounterEmailValidator : AbstractValidator<ReceiveEncounterEmailCommand>
{
    public ReceiveEncounterEmailValidator()
    {
        RuleFor(x => x.EncounterEmail).NotEmpty();
    }
}

internal sealed class ReceiveEncounterEmailHandler : IRequestHandler<ReceiveEncounterEmailCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly S3FileService _fileService;

    public ReceiveEncounterEmailHandler(IDocumentStore store, S3FileService fileService)
    {
        _store = store;
        _fileService = fileService;
    }

    public async Task<Unit> Handle(ReceiveEncounterEmailCommand command, CancellationToken cancellationToken)
    {
        var encounterId = command.EncounterEmail.Split('@')[0];
        encounterId = encounterId.Replace("Toro Health <", "");

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Encounter encounter = await session
            .Include<Domain.Encounter>(x => x.PatientId)
            .LoadAsync<Domain.Encounter>(encounterId, cancellationToken);
        Guard.AgainstNotFound(encounter, new("Encounter.NotFound", $"Encounter with 'id:' {encounterId} not found!"));

        Domain.Patient patient = await session
            .LoadAsync<Domain.Patient>(encounter.PatientId, cancellationToken);

        Sender sender = new Sender(true, patient.Id, patient.FullName, patient.Email);

        var attachments = new List<string>();
        if (command.Attachments != null)
        {
            foreach (var attachment in command.Attachments)
            {
                var filePath = $"patients/{encounter.PatientId}/encounters/{encounterId}/emails/{attachment.FileName}";
                await using var stream = attachment.OpenReadStream();
                await _fileService.UploadFile(stream, attachment.ContentType, Config.S3.AppFilesBucketName, filePath);
                attachments.Add(filePath);
            }
        }
        EncounterEmail message = EncounterEmail.Create(encounter.PatientId, sender, encounterId, command.Subject,
            ParseReplyText(command.Body), DateTimeOffset.UtcNow, attachments, null);

        await session.StoreAsync(message, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }

    private static string ParseReplyText(string rawText)
    {
        // Very basic implementation
        // If you want more accurate parsing, integrate EmailReplyParser library
        var splitter = new[] { "On", "wrote:" };
        foreach (var splitWord in splitter)
        {
            var idx = rawText.IndexOf(splitWord);
            if (idx > 0)
            {
                return rawText.Substring(0, idx).Trim();
            }
        }
        return rawText.Trim();
    }
}