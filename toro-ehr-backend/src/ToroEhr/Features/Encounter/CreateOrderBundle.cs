using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record CreateOrderBundleCommand(string EncounterId, string PatientId, string BundleTemplateId) : AuthRequest<string>;

internal class CreateOrderBundleAuth : IAuth<CreateOrderBundleCommand, string>
{
    public CreateOrderBundleAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class CreateOrderBundleValidator : AbstractValidator<CreateOrderBundleCommand>
{
    public CreateOrderBundleValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.BundleTemplateId).NotEmpty();
    }
}

internal class CreateOrderBundleHandler : IRequestHandler<CreateOrderBundleCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrderBundleHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrderBundleCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderBundleTemplate bundleTemplate = await session.LoadAsync<OrderBundleTemplate>(command.BundleTemplateId);
        Guard.AgainstNotFound(bundleTemplate, new("OrderBundleTemplate.NotFound", $"Order Bundle Template with 'id:' {command.BundleTemplateId} not found!"));

        //todo: flat order bundle entries
        var nestedEntries = await MapEntriesAsync(bundleTemplate.Steps.SelectMany(x => x.OrderEntries).ToList(), command.EncounterId, command.PatientId,
            bundleTemplate.Priority);

        OrderBundle orderBundle = OrderBundle.Create(command.EncounterId, command.PatientId, command.BundleTemplateId, bundleTemplate.Name, bundleTemplate.Priority,
            nestedEntries, DateTimeOffset.UtcNow, _user.EmployeeId!);

        await session.StoreAsync(orderBundle, cancellationToken);
        await session.SaveChangesAsync();

        return orderBundle.Id;
    }

    private async Task<List<Order>> MapEntriesAsync(IEnumerable<OrderEntry> orderEntries, string encounterId, string patientId, string priority)
    {
        var result = new List<Order>();

        foreach (var orderEntry in orderEntries)
        {
            switch (orderEntry)
            {
                case MedicationOrderEntry medOrderEntry:
                    result.Add(OrderMedication.Create(encounterId, patientId, medOrderEntry.MedicationId, medOrderEntry.IngredientId, medOrderEntry.Frequency,
                        medOrderEntry.CustomFrequency, medOrderEntry.Duration, medOrderEntry.Prn, medOrderEntry.PrnReason, DateTimeOffset.UtcNow, 
                        string.Empty, medOrderEntry.Name, DateTimeOffset.UtcNow, _user.EmployeeId!));
                    break;

                case LabOrderEntry labOrderEntry:
                    result.Add(OrderLab.Create(encounterId, patientId, labOrderEntry.LoincCodeId, labOrderEntry.Specimen, labOrderEntry.Note,
                        labOrderEntry.Fasting, labOrderEntry.Repeat, labOrderEntry.Name, priority, DateTimeOffset.UtcNow, _user.EmployeeId!));
                    break;


                case ProcedureOrderEntry procedureOrderEntry:
                    result.Add(OrderProcedure.Create(encounterId, patientId, procedureOrderEntry.SnomedCodeId, procedureOrderEntry.Note,
                        procedureOrderEntry.Fasting, procedureOrderEntry.Repeat, procedureOrderEntry.Name, priority, DateTimeOffset.UtcNow, _user.EmployeeId!));
                    break;

                case BundleOrderEntry bundleOrderEntry:
                    var nestedEntries = await MapEntriesAsync(bundleOrderEntry.OrderEntries, encounterId, patientId, priority); // 🔁 Recursive step
                    result.Add(OrderBundle.Create(encounterId, patientId, bundleOrderEntry.BundleId, bundleOrderEntry.Name, priority,
                        nestedEntries, DateTimeOffset.UtcNow, _user.EmployeeId!));
                    break;

                default:
                    break;
            }
        }

        return result;
    }
}