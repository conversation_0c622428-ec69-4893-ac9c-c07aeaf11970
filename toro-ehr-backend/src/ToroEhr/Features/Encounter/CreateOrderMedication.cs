using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;

namespace ToroEhr.Features.Encounter;

public record CreateOrderMedicationCommand(string EncounterId, string PatientId, string MedicationId, string IngredientId,
    string Frequency, string CustomFrequency, string Duration, bool Prn, string? PrnReason, DateTimeOffset StartTime, string Instructions) : AuthRequest<string>;

internal class CreateOrderMedicationAuth : IAuth<CreateOrderMedicationCommand, string>
{
    public CreateOrderMedicationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class CreateOrderMedicineValidator : AbstractValidator<CreateOrderMedicationCommand>
{
    public CreateOrderMedicineValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.MedicationId).NotEmpty();
        RuleFor(x => x.Frequency).NotEmpty();
        RuleFor(x => x.Duration).NotEmpty();
        RuleFor(x => x.StartTime).NotEmpty();
    }
}

internal class CreateOrderMedicationHandler : IRequestHandler<CreateOrderMedicationCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrderMedicationHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrderMedicationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Medication medication = await session.LoadAsync<Domain.Medication>(command.MedicationId);
        Guard.AgainstNotFound(medication, new("Medication.NotFound", $"Medication with 'id:' {command.MedicationId} not found!"));


        OrderMedication orderMedication = OrderMedication.Create(command.EncounterId, command.PatientId, command.MedicationId, command.IngredientId,
            command.Frequency, command.CustomFrequency, command.Duration, command.Prn, command.PrnReason, command.StartTime, command.Instructions, medication.TermString, 
            DateTimeOffset.UtcNow, _user.EmployeeId!);

        await session.StoreAsync(orderMedication, cancellationToken);
        await session.SaveChangesAsync();

        return orderMedication.Id;
    }
}