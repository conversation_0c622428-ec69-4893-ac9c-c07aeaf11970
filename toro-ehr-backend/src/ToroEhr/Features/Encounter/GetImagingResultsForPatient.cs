using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record GetImagingResultsForPatientQuery(string PatientId) : AuthRequest<List<ImagingResultResponse>>;

internal class GetImagingResultsForPatientAuth : IAuth<GetImagingResultsForPatientQuery, List<ImagingResultResponse>>
{
    public GetImagingResultsForPatientAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public record ImagingResultResponse(
    string Id,
    string Status,
    string PatientId,
    string? EncounterId,
    DateTime? EffectiveDate,
    DateTime? Issued,
    string? PerformerId,
    string Reference,
    List<ImagingFileResponse> Files
);

public record ImagingFileResponse(
    string ContentType,
    string? Title,
    string? Url,
    bool Seen
);

internal class GetImagingResultsForPatientHandler : IRequestHandler<GetImagingResultsForPatientQuery, List<ImagingResultResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetImagingResultsForPatientHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<ImagingResultResponse>> Handle(GetImagingResultsForPatientQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<ImagingResult> imagingResults = await session.Query<ImagingResult>()
            .Where(x => x.PatientId == query.PatientId)
            .ToListAsync(cancellationToken);

        return imagingResults.Select(entity => new ImagingResultResponse(
            entity.Id,
            entity.Status,
            entity.PatientId,
            entity.EncounterId,
            entity.EffectiveDate,
            entity.Issued,
            entity.PerformerId,
            entity.Reference,
            entity.Files.Select(f => new ImagingFileResponse(f.ContentType, f.Title, f.Url, f.Seen)).ToList()
        )).ToList();
    }
}