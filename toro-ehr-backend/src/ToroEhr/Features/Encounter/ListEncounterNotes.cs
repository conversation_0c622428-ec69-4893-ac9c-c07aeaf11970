using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record ListEncounterNotesQuery(string EncounterId) : AuthRequest<List<PatientNoteResponse>>;

internal sealed class ListEncounterNotesAuth : IAuth<ListEncounterNotesQuery, List<PatientNoteResponse>>
{
    public ListEncounterNotesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListEncounterNotesHandler : IRequestHandler<ListEncounterNotesQuery, List<PatientNoteResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListEncounterNotesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<PatientNoteResponse>> Handle(ListEncounterNotesQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<Domain.Note> notes = await session
            .Query<Domain.Note>()
            .Include(x => x.PractitionerId)
            .Where(x => x.EncounterId == query.EncounterId && x.IsLatest)
            .OrderByDescending(x => x.SignedAt)
            .ToListAsync(token: cancellationToken);

        List<PatientNoteResponse> result = [];

        foreach (var note in notes)
        {
            var practitioner = await session.LoadAsync<Domain.Employee>(note.PractitionerId, cancellationToken);
            
            result.Add(new PatientNoteResponse(
                note.Id,
                note.NoteId,
                note.Version,
                note.Name,
                note.Classification,
                practitioner!.ShortName,
                note.SignedAt,
                note.Fields.Select(f => new NoteFieldResponse(f.Name, f.Value, true)).ToList(),
                note.CptCode,
                note.IcdCode
            ));
        }
        return result;
    }
}