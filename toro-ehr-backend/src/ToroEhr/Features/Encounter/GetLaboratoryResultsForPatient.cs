using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;

namespace ToroEhr.Features.Encounter;

public record GetLaboratoryResultsForPatientQuery(string PatientId) : AuthRequest<List<LaboratoryResultResponse>>;

internal class GetLaboratoryResultsForPatientAuth : IAuth<GetLaboratoryResultsForPatientQuery, List<LaboratoryResultResponse>>
{
    public GetLaboratoryResultsForPatientAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public record LaboratoryResultResponse(
    string Status,
    string? CodeSystem,
    string? Code,
    string? CodeDisplay,
    string PatientId,
    string? EncounterId,
    DateTimeOffset? EffectiveDateTime,
    DateTimeOffset? Issued,
    string? PerformerId,
    string? ValueQuantity,
    string? ValueUnit,
    string? InterpretationCode,
    string? InterpretationDisplay,
    string? ReferenceRangeLow,
    string? ReferenceRangeHigh,
    string? ReferenceRangeUnit,
    string? Comments,
    bool Seen
);

internal class GetLaboratoryResultsForPatientHandler : IRequestHandler<GetLaboratoryResultsForPatientQuery, List<LaboratoryResultResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetLaboratoryResultsForPatientHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<LaboratoryResultResponse>> Handle(GetLaboratoryResultsForPatientQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<LaboratoryResult> labResults = await session.Query<Domain.LaboratoryResult>()
            .Where(x => x.PatientId == query.PatientId)
            .ToListAsync(cancellationToken);

        return labResults.Select(entity => new LaboratoryResultResponse(
            entity.Status,
            entity.CodeSystem,
            entity.Code,
            entity.CodeDisplay,
            entity.PatientId,
            entity.EncounterId,
            entity.EffectiveDateTime,
            entity.Issued,
            entity.PerformerId,
            entity.ValueQuantity,
            entity.ValueUnit,
            entity.InterpretationCode,
            entity.InterpretationDisplay,
            entity.ReferenceRangeLow,
            entity.ReferenceRangeHigh,
            entity.ReferenceRangeUnit,
            entity.Comments,
            entity.Seen
        )).ToList();
    }
}