using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Domain;

namespace ToroEhr.Features.Encounter;

public sealed record MarkLaboratoryResultAsSeenCommand(string PatientId, DateTimeOffset Date)
    : AuthRequest<Unit>;

internal sealed class MarkLaboratoryResultAsSeenCommandAuth : IAuth<UpdateScratchTextCommand, Unit>
{
    public MarkLaboratoryResultAsSeenCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class MarkLaboratoryResultAsSeenCommandValidator : AbstractValidator<MarkLaboratoryResultAsSeenCommand>
{
    public MarkLaboratoryResultAsSeenCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
    }
}

internal sealed class MarkLaboratoryResultAsSeenHandler : IRequestHandler<MarkLaboratoryResultAsSeenCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public MarkLaboratoryResultAsSeenHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(MarkLaboratoryResultAsSeenCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<LaboratoryResult> labResults = await session.Query<LaboratoryResult>()
            .Where(x => x.PatientId == command.PatientId && x.EffectiveDateTime == command.Date)
            .ToListAsync(cancellationToken);
        
        foreach (var labResult in labResults)
        {
            labResult.MarkAsSeen();
            await session.StoreAsync(labResult, cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}