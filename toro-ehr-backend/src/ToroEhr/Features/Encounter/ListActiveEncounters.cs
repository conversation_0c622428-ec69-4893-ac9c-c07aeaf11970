using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record ListActiveEncountersQuery : AuthRequest<List<EncounterResponse>>;

public sealed record EncounterResponse(
    string Id,
    string? ScratchText,
    string PatientId,
    string Email,
    string FullName,
    DateTime PatientBirthday,
    string? PhoneNumber,
    string? PreferredContactMethod,
    DateTimeOffset? previouesEncounterDate);

internal sealed class ListActiveEncountersAuth : IAuth<ListActiveEncountersQuery, List<EncounterResponse>>
{
    public ListActiveEncountersAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListActiveEncountersHandler : IRequestHandler<ListActiveEncountersQuery, List<EncounterResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListActiveEncountersHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterResponse>> Handle(ListActiveEncountersQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Domain.Encounter> dbQuery = session.Query<Domain.Encounter>()
            .Include(x => x.PatientId)
            .Where(x => x.Status == EncounterStatus.InProgress.Name && x.LocationId == _user.SelectedLocationId);

        if (_user.SelectedUserRole == EmployeeRole.Practitioner)
        {
            dbQuery = dbQuery.Where(x => x.PractitionerId == _user.EmployeeId);
        }

        List<Domain.Encounter> encounters = await dbQuery.ToListAsync(token: cancellationToken);
        IEnumerable<string> patientIds = encounters.Select(e => e.PatientId).Distinct();
        Dictionary<string, Domain.Patient> patientsDict =
            await session.LoadAsync<Domain.Patient>(patientIds.ToList(), cancellationToken);

        List<EncounterResponse> encountersResponses = new List<EncounterResponse>();

        foreach (var encounter in encounters)
        {
           var lastTwoEncounters = await session.Query<Domain.Encounter>()
                .Where(x => x.PatientId == encounter.PatientId && x.PractitionerId == encounter.PractitionerId &&
                x.LocationId == encounter.LocationId)
                .OrderByDescending(x => x.StartAt)
                .Take(2)
                .ToListAsync(cancellationToken);

            Domain.Encounter? previousEncounter = null;

            if (lastTwoEncounters.Count == 2)
            {
                previousEncounter = lastTwoEncounters[1];
            }

            encountersResponses.Add(ToEncounterResponse(encounter, patientsDict[encounter.PatientId],
                previousEncounter?.StartAt));
        }

        return encountersResponses;
    }

    private static EncounterResponse ToEncounterResponse(Domain.Encounter encounter, Domain.Patient patient,
        DateTimeOffset? previouesEncounterDate)
    {
        return new EncounterResponse(
            encounter.Id,
            encounter.ScratchText,
            patient.Id,
            patient.Email,
            patient.FullName,
            patient.Birthday,
            patient.PhoneNumbers.FirstOrDefault(x => x.IsPrimary)?.Number,
            patient.PreferredContactMethod,
            previouesEncounterDate
        );
    }
}