using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Features.Encounter.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record UpdateScratchTextCommand(string PatientId, string ScratchText)
    : AuthRequest<Unit>;

internal sealed class UpdateScratchTextCommandAuth : IAuth<UpdateScratchTextCommand, Unit>
{
    public UpdateScratchTextCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class UpdateScratchTextCommandValidator : AbstractValidator<UpdateScratchTextCommand>
{
    public UpdateScratchTextCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
    }
}

internal sealed class UpdateScratchTextHandler : IRequestHandler<UpdateScratchTextCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public UpdateScratchTextHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(UpdateScratchTextCommand command, CancellationToken cancellationToken)
    {

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Encounter encounter = await session.Query<Domain.Encounter>()
            .Where(x => x.PatientId == command.PatientId)
            .FirstOrDefaultAsync(cancellationToken);
        Guard.AgainstNotFound(encounter, EncounterErrors.NotFoundByPatientId(command.PatientId));
        encounter.UpdateScratchText(command.ScratchText);

        await session.StoreAsync(encounter, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}