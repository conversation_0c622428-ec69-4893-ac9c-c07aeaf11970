using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record EditOrderMedicationCommand(string Id, string? BundleId, string MedicationId, string IngredientId, string Frequency, string? CustomFrequency,
        string Duration, bool Prn, string? PrnReason, DateTimeOffset StartTime, string Instructions) : AuthRequest<Unit>;

internal class EditOrderMedicationAuth : IAuth<EditOrderMedicationCommand, Unit>
{
    public EditOrderMedicationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class EditOrderMedicationValidator : AbstractValidator<EditOrderMedicationCommand>
{
    public EditOrderMedicationValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.MedicationId).NotEmpty();
        RuleFor(x => x.Frequency).NotEmpty();
        RuleFor(x => x.Duration).NotEmpty();
        RuleFor(x => x.StartTime).NotEmpty();
    }
}

internal class EditOrderMedicationHandler : IRequestHandler<EditOrderMedicationCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditOrderMedicationHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditOrderMedicationCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Medication medication = await session.LoadAsync<Domain.Medication>(command.MedicationId);
        Guard.AgainstNotFound(medication, new("Medication.NotFound", $"Medication with 'id:' {command.MedicationId} not found!"));

        if (command.BundleId.IsNotNullOrWhiteSpace())
        {
            OrderBundle orderBundle = await session.LoadAsync<OrderBundle>(command.BundleId);
            Guard.AgainstNotFound(orderBundle, new("Order.NotFound", $"Order Bundle with 'id:' {command.Id} not found!"));

            var bundleOrderMedication = orderBundle.Orders.FirstOrDefault(x => x.Id == command.Id);
            if (bundleOrderMedication is OrderMedication om)
            {
                om.Update(command.MedicationId, command.IngredientId, command.Frequency, command.CustomFrequency, command.Duration, command.Prn, 
                    command.PrnReason, command.StartTime, command.Instructions, medication.TermString, bundleOrderMedication.Priority);
            }
        }
        else
        {
            OrderMedication orderMedication = await session.LoadAsync<OrderMedication>(command.Id);
            Guard.AgainstNotFound(orderMedication, new("Order.NotFound", $"Order with 'id:' {command.Id} not found!"));

            orderMedication.Update(command.MedicationId, command.IngredientId, command.Frequency, command.CustomFrequency, command.Duration, command.Prn, command.PrnReason, command.StartTime,
                command.Instructions, medication.TermString, orderMedication.Priority);
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}