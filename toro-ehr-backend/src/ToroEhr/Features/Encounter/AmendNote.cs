using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record AmendNoteCommand(
    string Id,
    string PatientId,
    string EncounterId,
    List<NoteFieldRequest> Fields,
    string IcdCode,
    string CptCode)
    : AuthRequest<Unit>;

internal sealed class AmendNoteCommandAuth : IAuth<AmendNoteCommand, Unit>
{
    public AmendNoteCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class AmendNoteCommandValidator : AbstractValidator<AmendNoteCommand>
{
    public AmendNoteCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
    }
}

internal sealed class AmendNoteHandler : IRequestHandler<AmendNoteCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public AmendNoteHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(AmendNoteCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<Note> notes =
            await session.Query<Note>().Where(x => x.NoteId == command.Id).ToListAsync(cancellationToken);
        
        Note current = notes.First(x => x.IsLatest);
        current.MarkAsNotLatest();

        Note amended = Note.CreateNewVersion(
            current,
            command.Fields.Select(x => new NoteField(x.Name, x.Value)).ToList(),
            command.CptCode,
            command.IcdCode,
            command.Timestamp
        );

        await session.StoreAsync(amended, cancellationToken);
        await session.StoreAsync(current, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}