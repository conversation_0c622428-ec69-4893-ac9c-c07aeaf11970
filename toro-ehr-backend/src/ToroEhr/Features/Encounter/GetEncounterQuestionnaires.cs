using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record GetEncounterQuestionnairesQuery(string EncounterId)
    : AuthRequest<List<EncounterQuestionnaireResponse>>;

internal class
    GetEncounterQuestionnairesAuth : IAuth<GetEncounterQuestionnairesQuery, List<EncounterQuestionnaireResponse>>
{
    public GetEncounterQuestionnairesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal class
    GetEncounterQuestionnairesHandler : IRequestHandler<GetEncounterQuestionnairesQuery,
    List<EncounterQuestionnaireResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetEncounterQuestionnairesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterQuestionnaireResponse>> Handle(GetEncounterQuestionnairesQuery query,
        CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        var patientId = (await session.LoadAsync<Domain.Encounter>(query.EncounterId, cancellationToken)).PatientId;

        var questionnaireResponses = await session.Query<Domain.QuestionnaireResponse>()
            .Include(x => x.QuestionnaireId)
            .Where(x => x.EncounterId == query.EncounterId ||
                        (x.EncounterId == null && x.PatientId == patientId))
            .ToListAsync(token: cancellationToken);

        var questionnaireIds = questionnaireResponses
            .Select(x => x.QuestionnaireId)
            .Distinct()
            .ToList();

        var questionnaires = await session.LoadAsync<Domain.Questionnaire>(questionnaireIds, cancellationToken);

        return questionnaireResponses
            .Select(qr =>
            {
                var questionnaire = questionnaires.First(x => x.Key == qr.QuestionnaireId).Value;

                return new EncounterQuestionnaireResponse(
                    questionnaire.Title,
                    questionnaire.Questions.Select(q =>
                    {
                        var questionAnswers = qr.Answers
                            .FirstOrDefault(a => a.QuestionId == q.Id)?.Values ?? [];

                        return new QuestionResponse(q.Text, questionAnswers);
                    }).ToList());
            })
            .ToList();
    }
}