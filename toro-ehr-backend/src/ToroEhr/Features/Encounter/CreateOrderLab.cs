using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Enums;

namespace ToroEhr.Features.Encounter;

public record CreateOrderLabCommand(string EncounterId, string PatientId, string LoincCodeId, string Specimen,
    string Note, bool Fasting, bool Repeat, string Priority) : AuthRequest<string>;

internal class CreateOrderLabAuth : IAuth<CreateOrderLabCommand, string>
{
    public CreateOrderLabAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class CreateOrderLabValidator : AbstractValidator<CreateOrderLabCommand>
{
    public CreateOrderLabValidator()
    {
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.LoincCodeId).NotEmpty();
        RuleFor(x => x.Specimen).NotEmpty();
        RuleFor(x => x.Priority).NotEmpty();
    }
}

internal class CreateOrderLabHandler : IRequestHandler<CreateOrderLabCommand, string>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public CreateOrderLabHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<string> Handle(CreateOrderLabCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.LoincCode loincCode = await session.LoadAsync<Domain.LoincCode>(command.LoincCodeId);
        Guard.AgainstNotFound(loincCode, new("LoincCode.NotFound", $"Loin cCode with 'id:' {command.LoincCodeId} not found!"));


        OrderLab orderMedication = OrderLab.Create(command.EncounterId, command.PatientId, command.LoincCodeId, command.Specimen,
            command.Note, command.Fasting, command.Repeat, loincCode.DisplayName ?? string.Empty, command.Priority,
            DateTimeOffset.UtcNow,  _user.EmployeeId!);

        await session.StoreAsync(orderMedication, cancellationToken);
        await session.SaveChangesAsync();

        return orderMedication.Id;
    }
}