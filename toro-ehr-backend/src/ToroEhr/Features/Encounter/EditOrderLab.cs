using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record EditOrderLabCommand(string Id, string? BundleId, string LoincCodeId, string Specimen,
    string Note, bool Fasting, bool Repeat, string Priority) : AuthRequest<Unit>;

internal class EditOrderLabAuth : IAuth<EditOrderLabCommand, Unit>
{
    public EditOrderLabAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class EditOrderLabValidator : AbstractValidator<EditOrderLabCommand>
{
    public EditOrderLabValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.LoincCodeId).NotEmpty();
        RuleFor(x => x.Specimen).NotEmpty();
        RuleFor(x => x.Priority).NotEmpty();
    }
}

internal class EditOrderLabHandler : IRequestHandler<EditOrderLabCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditOrderLabHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditOrderLabCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.LoincCode loincCode = await session.LoadAsync<Domain.LoincCode>(command.LoincCodeId);
        Guard.AgainstNotFound(loincCode, new("LoincCode.NotFound", $"LoincCode with 'id:' {command.LoincCodeId} not found!"));

        if (command.BundleId.IsNotNullOrWhiteSpace())
        {
            OrderBundle orderBundle = await session.LoadAsync<OrderBundle>(command.BundleId);
            Guard.AgainstNotFound(orderBundle, new("Order.NotFound", $"Order Bundle with 'id:' {command.Id} not found!"));

            var bundleOrderLab = orderBundle.Orders.FirstOrDefault(x => x.Id == command.Id);
            if (bundleOrderLab is OrderLab ol)
            {
                ol.Update(command.LoincCodeId, command.Specimen, command.Note, command.Fasting, command.Repeat, loincCode.DisplayName ?? string.Empty, 
                    command.Priority);
            }
        }
        else
        {
            OrderLab orderLab = await session.LoadAsync<OrderLab>(command.Id);
            Guard.AgainstNotFound(orderLab, new("Order.NotFound", $"Order with 'id:' {command.Id} not found!"));

            orderLab.Update(command.LoincCodeId, command.Specimen, command.Note, command.Fasting, command.Repeat, loincCode.DisplayName ?? string.Empty, 
                command.Priority);
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}