using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Indexes;

namespace ToroEhr.Features.Encounter;

public record GetEncounterDetailsQuery(string EncounterId) : AuthRequest<EncounterDetailsResponse>;

internal class GetEncounterDetailsAuth : IAuth<GetEncounterDetailsQuery, EncounterDetailsResponse>
{
    public GetEncounterDetailsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

public record EncounterDetailsResponse(
    string Id,
    DateTime EncounterDate,
    string EncounterLocationName,
    string PatientId,
    string PatientMrn,
    string PatientName,
    DateTime PatientDateOfBirth,
    string? PatientAddress,
    string PatientPhoneNumber,
    string PatientSex,
    string PractitionerName,
    List<PatientNoteResponse> Notes,
    List<VitalSignResponse> VitalSigns,
    List<EncounterQuestionnaireResponse> Questionnaires,
    List<EncounterMedicationResponse> Medications);

public record EncounterMedicationResponse(string Name, string Frequency, string Duration, string Instructions);

internal class GetEncounterDetailsHandler : IRequestHandler<GetEncounterDetailsQuery, EncounterDetailsResponse>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetEncounterDetailsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<EncounterDetailsResponse> Handle(GetEncounterDetailsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // Load core encounter data
        var (encounter, patient, practitioner, location) = await LoadEncounterCoreData(session, query.EncounterId, cancellationToken);

        // Load all related data
        var notes = await LoadNotes(session, query.EncounterId, cancellationToken);
        var vitalSigns = await LoadVitalSigns(session, query.EncounterId, cancellationToken);
        var questionnaireResponses = await LoadQuestionnaireResponses(session, query.EncounterId, encounter.PatientId, cancellationToken);
        var medications = await LoadMedications(session, query.EncounterId, cancellationToken);

        // Process all data into response models
        var noteResponses = ProcessNotes(notes, practitioner);
        var vitalSignResponses = ProcessVitalSigns(vitalSigns);
        var questionnaireResponseList = await ProcessQuestionnaires(session, questionnaireResponses, cancellationToken);
        var medicationResponses = ProcessMedications(medications);

        return new EncounterDetailsResponse(
            encounter.Id,
            encounter.StartAt.DateTime,
            location.Name,
            patient.Id,
            patient.Mrn,
            patient.FullName,
            patient.Birthday.Date,
            patient.Address?.Formatted,
            patient.PhoneNumbers.First(x => x.IsPrimary).Number,
            patient.BirthSex!,
            practitioner.FullName,
            noteResponses,
            vitalSignResponses,
            questionnaireResponseList,
            medicationResponses);
    }

    private async Task<(Domain.Encounter encounter, Domain.Patient patient, Domain.Employee practitioner, Domain.Location location)>
        LoadEncounterCoreData(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        Domain.Encounter encounter = await session
            .Include<Domain.Encounter>(x => x.PatientId)
            .Include<Domain.Encounter>(x => x.PractitionerId)
            .Include<Domain.Encounter>(x => x.LocationId)
            .LoadAsync<Domain.Encounter>(encounterId, cancellationToken);

        Guard.AgainstNotFound(encounter, EncounterErrors.NotFoundById(encounterId));

        var patient = await session.LoadAsync<Domain.Patient>(encounter.PatientId, cancellationToken);
        var practitioner = await session.LoadAsync<Domain.Employee>(encounter.PractitionerId, cancellationToken);
        var location = await session.LoadAsync<Domain.Location>(encounter.LocationId, cancellationToken);

        return (encounter, patient, practitioner, location);
    }

    private async Task<List<Domain.Note>> LoadNotes(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        return await session
            .Query<Domain.Note>()
            .Where(x => x.EncounterId == encounterId && x.IsLatest)
            .OrderByDescending(x => x.SignedAt)
            .ToListAsync(token: cancellationToken);
    }

    private async Task<List<Domain.VitalSign>> LoadVitalSigns(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        return await session
            .Query<Domain.VitalSign>()
            .Where(x => x.EncounterId == encounterId)
            .OrderByDescending(x => x.Date)
            .ToListAsync(cancellationToken);
    }

    private async Task<List<Domain.QuestionnaireResponse>> LoadQuestionnaireResponses(IAsyncDocumentSession session, string encounterId, string patientId, CancellationToken cancellationToken)
    {
        return await session
            .Query<Domain.QuestionnaireResponse>()
            .Include(x => x.QuestionnaireId)
            .Where(x => x.EncounterId == encounterId ||
                        (x.EncounterId == null && x.PatientId == patientId))
            .ToListAsync(token: cancellationToken);
    }

    private async Task<List<Domain.OrderMedication>> LoadMedications(IAsyncDocumentSession session, string encounterId, CancellationToken cancellationToken)
    {
        // Load medication orders using index
        List<Orders_ByEncounter.Entry> medicationOrderEntries = await session
            .Query<Orders_ByEncounter.Entry, Orders_ByEncounter>()
            .Where(x => x.EncounterId == encounterId && x.OrderType == nameof(Domain.OrderMedication))
            .ToListAsync(cancellationToken);

        var medicationOrderIds = medicationOrderEntries.Select(x => x.OrderId).ToList();
        var medicationOrdersDict = await session.LoadAsync<Domain.OrderMedication>(medicationOrderIds, cancellationToken);

        return medicationOrdersDict.Values.ToList();
    }

    private List<PatientNoteResponse> ProcessNotes(List<Domain.Note> notes, Domain.Employee practitioner)
    {
        List<PatientNoteResponse> noteResponses = [];
        foreach (var note in notes)
        {
            noteResponses.Add(new PatientNoteResponse(
                note.Id,
                note.NoteId,
                note.Version,
                note.Name,
                note.Classification,
                practitioner.ShortName,
                note.SignedAt,
                note.Fields.Select(f => new NoteFieldResponse(f.Name, f.Value, true)).ToList(),
                note.CptCode,
                note.IcdCode));
        }
        return noteResponses;
    }

    private List<VitalSignResponse> ProcessVitalSigns(List<Domain.VitalSign> vitalSigns)
    {
        return vitalSigns.Select(vs => new VitalSignResponse(
            vs.Date,
            vs.Measurements.Select(m => new MeasurementResponse(m.Type, m.Value)).ToList())).ToList();
    }

    private async Task<List<EncounterQuestionnaireResponse>> ProcessQuestionnaires(IAsyncDocumentSession session, List<Domain.QuestionnaireResponse> questionnaireResponses, CancellationToken cancellationToken)
    {
        var questionnaireIds = questionnaireResponses
            .Select(x => x.QuestionnaireId)
            .Distinct()
            .ToList();

        var questionnaires = await session.LoadAsync<Domain.Questionnaire>(questionnaireIds, cancellationToken);

        return questionnaireResponses
            .Select(qr =>
            {
                var questionnaire = questionnaires.First(x => x.Key == qr.QuestionnaireId).Value;

                return new EncounterQuestionnaireResponse(
                    questionnaire.Title,
                    questionnaire.Questions.Select(q =>
                    {
                        var questionAnswers = qr.Answers
                            .FirstOrDefault(a => a.QuestionId == q.Id)?.Values ?? [];

                        return new QuestionResponse(q.Text, questionAnswers);
                    }).ToList());
            })
            .ToList();
    }

    private List<EncounterMedicationResponse> ProcessMedications(List<Domain.OrderMedication> medications)
    {
        List<EncounterMedicationResponse> medicationResponses = [];
        foreach (var medicationOrder in medications)
        {
            var frequency = medicationOrder.Frequency.ToLower() == "custom"
                ? medicationOrder.CustomFrequency ?? medicationOrder.Frequency
                : medicationOrder.Frequency;

            medicationResponses.Add(new EncounterMedicationResponse(
                medicationOrder.Name,
                frequency,
                medicationOrder.Duration,
                medicationOrder.Instructions));
        }
        return medicationResponses;
    }
}