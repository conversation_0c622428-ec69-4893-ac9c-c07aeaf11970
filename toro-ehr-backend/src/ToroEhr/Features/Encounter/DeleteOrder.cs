using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record DeleteOrderCommand(string Id, string? BundleId) : AuthRequest<Unit>;

internal class DeleteOrderAuth : IAuth<DeleteOrderCommand, Unit>
{
    public DeleteOrderAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class DeleteOrderValidator : AbstractValidator<DeleteOrderCommand>
{
    public DeleteOrderValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

internal class DeleteOrderHandler : IRequestHandler<DeleteOrderCommand, Unit>
{
    private readonly IDocumentStore _store;

    public DeleteOrderHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(DeleteOrderCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        if (command.BundleId.IsNotNullOrWhiteSpace())
        {
            OrderBundle bundleOrder = await session.LoadAsync<OrderBundle>(command.BundleId, cancellationToken);
            
            Order? nestedOrder = bundleOrder.Orders.FirstOrDefault(x => x.Id == command.Id);
            Guard.AgainstNotFound(nestedOrder, new("Order.NotFound", $"Order with 'id:' {command.Id} not found!"));

            bundleOrder.Orders.Remove(nestedOrder);
        } else
        {
            Order order = await session.LoadAsync<Order>(command.Id, cancellationToken);

            session.Delete(order);
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}