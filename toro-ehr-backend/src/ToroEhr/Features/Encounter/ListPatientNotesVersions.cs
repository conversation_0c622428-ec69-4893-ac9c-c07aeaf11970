using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record ListPatientNotesVersionsQuery(string NoteId) : AuthRequest<List<PatientNoteResponse>>;

internal sealed class ListPatientNotesVersionsAuth : IAuth<ListPatientNotesVersionsQuery, List<PatientNoteResponse>>
{
    public ListPatientNotesVersionsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListPatientNotesVersionsHandler : IRequestHandler<ListPatientNotesVersionsQuery, List<PatientNoteResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientNotesVersionsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<PatientNoteResponse>> Handle(ListPatientNotesVersionsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        List<Domain.Note> notes = await session
            .Query<Domain.Note>()
            .Include(x => x.PractitionerId)
            .Where(x => x.NoteId == query.NoteId)
            .OrderByDescending(x => x.SignedAt)
            .ToListAsync(token: cancellationToken);

        List<PatientNoteResponse> result = [];

        foreach (var note in notes)
        {
            var practitioner = await session.LoadAsync<Domain.Employee>(note.PractitionerId, cancellationToken);
            
            result.Add(new PatientNoteResponse(
                note.Id,
                note.NoteId,
                note.Version,
                note.Name,
                note.Classification,
                practitioner!.ShortName,
                note.SignedAt,
                note.Fields.Select(f => new NoteFieldResponse(f.Name, f.Value, true)).ToList(),
                note.CptCode,
                note.IcdCode
            ));
        }
        return result;
    }
}