using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record EditOrderProcedureCommand(string Id, string? BundleId, string SnomedCodeId,
    string Note, bool Fasting, bool Repeat, string Priority) : AuthRequest<Unit>;

internal class EditOrderProcedureAuth : IAuth<EditOrderProcedureCommand, Unit>
{
    public EditOrderProcedureAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class EditOrderProcedureValidator : AbstractValidator<EditOrderProcedureCommand>
{
    public EditOrderProcedureValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
        RuleFor(x => x.SnomedCodeId).NotEmpty();
        RuleFor(x => x.Priority).NotEmpty();
    }
}

internal class EditOrderProcedureHandler : IRequestHandler<EditOrderProcedureCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditOrderProcedureHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditOrderProcedureCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.SnomedCode snomedCode = await session.LoadAsync<Domain.SnomedCode>(command.SnomedCodeId);
        Guard.AgainstNotFound(snomedCode, new("SnomedCode.NotFound", $"SnomedCode with 'id:' {command.SnomedCodeId} not found!"));

        if (command.BundleId.IsNotNullOrWhiteSpace())
        {
            OrderBundle orderBundle = await session.LoadAsync<OrderBundle>(command.BundleId);
            Guard.AgainstNotFound(orderBundle, new("Order.NotFound", $"Order Bundle with 'id:' {command.Id} not found!"));

            var bundleOrderLab = orderBundle.Orders.FirstOrDefault(x => x.Id == command.Id);
            if (bundleOrderLab is OrderProcedure op)
            {
                op.Update(command.SnomedCodeId, command.Note, command.Fasting, command.Repeat, snomedCode.Term ?? string.Empty,
                    command.Priority);
            }
        }
        else
        {
            OrderProcedure orderProcedure = await session.LoadAsync<OrderProcedure>(command.Id);
            Guard.AgainstNotFound(orderProcedure, new("Order.NotFound", $"Order with 'id:' {command.Id} not found!"));

            orderProcedure.Update(command.SnomedCodeId, command.Note, command.Fasting, command.Repeat, snomedCode.Term ?? string.Empty,
                command.Priority);
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}