using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Features.Encounter.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record MarkImagingResultAsSeenCommand(string PatientId, string Id, string Url)
    : AuthRequest<Unit>;

internal sealed class MarkImagingResultAsSeenCommandAuth : IAuth<UpdateScratchTextCommand, Unit>
{
    public MarkImagingResultAsSeenCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class MarkImagingResultAsSeenCommandValidator : AbstractValidator<MarkImagingResultAsSeenCommand>
{
    public MarkImagingResultAsSeenCommandValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.Url).NotEmpty();
    }
}

internal sealed class MarkImagingResultAsSeenHandler : IRequestHandler<MarkImagingResultAsSeenCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public MarkImagingResultAsSeenHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(MarkImagingResultAsSeenCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        ImagingResult imagingResult = await session.Query<ImagingResult>()
            .Where(x => x.Id == command.Id)
            .FirstOrDefaultAsync(cancellationToken);
        ImagingFile? imagingFile = imagingResult.Files.FirstOrDefault(x => x.Url == command.Url);
        Guard.AgainstNotFound(imagingFile, EncounterErrors.NotFoundImagingFile(command.Id));

        imagingFile.MarkAsSeen();
        await session.StoreAsync(imagingResult, cancellationToken);

        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}