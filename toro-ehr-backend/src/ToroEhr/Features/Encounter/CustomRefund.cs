using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record CustomRefundCommand(string TransactionId, decimal RefundAmount) : AuthRequest<ProcessPaymentResponse>;

internal sealed class CustomRefundCommandAuth : IAuth<CustomRefundCommand, ProcessPaymentResponse>
{
    public CustomRefundCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class CustomRefundCommandValidator : AbstractValidator<CustomRefundCommand>
{
    public CustomRefundCommandValidator()
    {
        RuleFor(x => x.TransactionId).NotEmpty();
        RuleFor(x => x.RefundAmount).GreaterThan(0).WithMessage("Refund amount must be greater than 0");
    }
}

internal sealed class CustomRefundHandler : IRequestHandler<CustomRefundCommand, ProcessPaymentResponse>
{
    private readonly IDocumentStore _store;
    private readonly IposCloudService _iposCloudService;

    public CustomRefundHandler(IDocumentStore store, IposCloudService iposCloudService)
    {
        _store = store;
        _iposCloudService = iposCloudService;
    }

    public async Task<ProcessPaymentResponse> Handle(CustomRefundCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        PaymentTransaction originalTransaction = await session
            .LoadAsync<PaymentTransaction>(command.TransactionId, cancellationToken);

        Guard.AgainstNotFound(originalTransaction, new("PaymentTransaction.NotFound", $"Payment transaction with id '{command.TransactionId}' not found"));

        // Only process approved transactions
        if (originalTransaction.Status != TransactionStatus.Approved.Name)
        {
            return new ProcessPaymentResponse(false, $"Cannot refund transaction with status: {originalTransaction.Status}");
        }

        // Validate refund amount doesn't exceed original amount
        if (command.RefundAmount > originalTransaction.Amount)
        {
            return new ProcessPaymentResponse(false, $"Refund amount ({command.RefundAmount:C}) cannot exceed original transaction amount ({originalTransaction.Amount:C})");
        }

        // Load encounter and location to get payment configuration
        Domain.Encounter encounter = await session
            .Include<Domain.Encounter>(x => x.LocationId)
            .LoadAsync<Domain.Encounter>(originalTransaction.EncounterId, cancellationToken);

        Guard.AgainstNotFound(encounter, new("Encounter.NotFound", $"Encounter with id '{originalTransaction.EncounterId}' not found"));

        Domain.Location location = await session.LoadAsync<Domain.Location>(encounter.LocationId, cancellationToken);

        if (location.IposPaysConfig == null)
        {
            return new ProcessPaymentResponse(false, "Payment is not enabled for this location");
        }

        // Always use IPOS Cloud for custom refunds
        try
        {
            bool isRefundSuccessful = await TryCustomRefund(originalTransaction, command.RefundAmount, location.IposPaysConfig);

            if (isRefundSuccessful)
            {
                // Create a new refund transaction for the custom amount
                PaymentTransaction refundTransaction = PaymentTransaction.CreateRefundTransaction(
                    originalTransaction.PatientId, originalTransaction.EncounterId, originalTransaction.PaymentMethod,
                    originalTransaction.CardType, originalTransaction.Last4, originalTransaction.ReferenceId,
                    command.RefundAmount, DateTime.UtcNow, TransactionStatus.Approved.Name, "00");

                await session.StoreAsync(refundTransaction, cancellationToken);
                await session.SaveChangesAsync(cancellationToken);
                return new ProcessPaymentResponse(true, $"Custom refund of {command.RefundAmount:C} processed successfully.");
            }
            else
            {
                return new ProcessPaymentResponse(false, "Custom refund failed.");
            }
        }
        catch (Exception ex)
        {
            return new ProcessPaymentResponse(false, $"Custom refund failed: {ex.Message}");
        }
    }

    private async Task<bool> TryCustomRefund(PaymentTransaction originalTransaction, decimal refundAmount, IposPaysConfig config)
    {
        if (string.IsNullOrEmpty(originalTransaction.ExternalReference))
        {
            throw new InvalidOperationException("TransactionId is required for IPOS Cloud refund operations");
        }

        var refundRequest = new IposCloudVoidRefundRequest(
            new MerchantAuthentication(config.CloudTpn, Utils.GenerateRandomId()),
            VoidRefundTransactionRequest.CreateRefundRequest(originalTransaction.ExternalReference, refundAmount));

        var response = await _iposCloudService.RefundTransactionAsync(refundRequest, config.CloudAuthKey);
        return response.Iposhpresponse?.ResponseCode == "200";
    }
}