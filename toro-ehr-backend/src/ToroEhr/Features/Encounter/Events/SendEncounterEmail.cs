using HandlebarsDotNet;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Features.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Services;
using ToroEhr.Templates.Email.EncounterMessage;

namespace ToroEhr.Features.Encounter.Events;

public record SendEncounterEmail(EncounterEmail EncounterEmail, Domain.Patient Patient) : BaseEventEntity;

public class SendEncounterEmailEventHandler : INotificationHandler<SendEncounterEmail>
{
    private readonly IDocumentStore _store;
    private readonly EmailService _emailService;

    public SendEncounterEmailEventHandler(IDocumentStore store, EmailService emailService)
    {
        _store = store;
        _emailService = emailService;
    }
    public async Task Handle(SendEncounterEmail notification, CancellationToken cancellationToken = new())
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var rootDir = Path.Combine("Templates", "Email", "EncounterMessage");
        var htmlTemplate = await File.ReadAllTextAsync(Path.Combine(rootDir, "index.html"), cancellationToken);
        var compiledTemplate = Handlebars.Compile(htmlTemplate);

        string email = compiledTemplate(new EncounterEmailMessage(Config.Application.Name, Config.Application.Url,
        $"{notification.Patient.FirstName} {notification.Patient.LastName}", notification.EncounterEmail.Message));
        string subject = notification.EncounterEmail.Subject;

        await _emailService.SendEmailAsync(notification.Patient.Email, subject, email, notification.EncounterEmail.Attachments,
            $"{notification.EncounterEmail.EncounterId}@{Config.SendGrid.InboundSubdomain}");
    }
}
