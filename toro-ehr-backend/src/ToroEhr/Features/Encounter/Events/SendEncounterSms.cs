using MediatR;
using ToroEhr.Domain;
using ToroEhr.Features.Shared;
using ToroEhr.Services;

namespace ToroEhr.Features.Encounter.Events;

public record SendEncounterSms(EncounterSms EncounterSms, Domain.Patient Patient) : BaseEventEntity;

public class SendEncounterSmsEventHandler : INotificationHandler<SendEncounterSms>
{
    private readonly SmsService _smsService;

    public SendEncounterSmsEventHandler(SmsService smsService)
    {
        _smsService = smsService;
    }
    public async Task Handle(SendEncounterSms notification, CancellationToken cancellationToken = new())
    {
        await _smsService.SendSmsAsync(notification.Patient.PhoneNumbers.First(x => x.IsPrimary).Number,
        notification.EncounterSms.Message);

    }
}