using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using Raven.Client.Documents.Linq;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record GetEncounterCommunicationMessagesQuery(string EncounterId) : AuthRequest<List<EncounterCommunicationMessageResponse>>;

internal class GetEncounterCommunicationMessagesAuth : IAuth<GetEncounterCommunicationMessagesQuery, List<EncounterCommunicationMessageResponse>>
{
    public GetEncounterCommunicationMessagesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

public record EncounterCommunicationMessageResponse(
    string Id,
    string PatientId,
    string Subject,
    string Message,
    List<string?> Attachments,
    string Type,
    DateTimeOffset SentAt,
    string SenderName,
    string RecipientName,
    bool SendByPatient,
    bool SeenByRecipient);


internal class GetEncounterCommunicationMessagesHandler : IRequestHandler<GetEncounterCommunicationMessagesQuery, List<EncounterCommunicationMessageResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public GetEncounterCommunicationMessagesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterCommunicationMessageResponse>> Handle(GetEncounterCommunicationMessagesQuery query, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();


        List<Communication_ByEncounter.Entry> entries = await session
            .Query<Communication_ByEncounter.Entry, Communication_ByEncounter>()
            .Include(x => x.PatientId)
            .Include(x => x.PractitionerId)
            .Include(x => x.Id)
            .Where(x => x.EncounterId == query.EncounterId)
            .OrderByDescending(x => x.SentAt)
            .ProjectInto<Communication_ByEncounter.Entry>()
            .ToListAsync(cancellationToken);

        var result = new List<EncounterCommunicationMessageResponse>();

        foreach (var entry in entries) 
        {
            Domain.CommunicationEntity message = await session.LoadAsync<Domain.CommunicationEntity>(entry.Id, cancellationToken);

            Domain.Patient patient = await session.LoadAsync<Domain.Patient>(entry.PatientId, cancellationToken);
            Domain.Employee practitioner = await session.LoadAsync<Domain.Employee>(entry.PractitionerId, cancellationToken);

            result.Add(new EncounterCommunicationMessageResponse(entry.Id, entry.PatientId, entry.Subject, entry.Message,
            entry.Attachments.Select(fp => Utils.GenerateS3PublicFileUrl(Config.S3.AppFilesBucketName, fp)).ToList(), entry.Type, entry.SentAt,
            message.Sender.SentByPatient ? patient.FullName : practitioner.FullName, message.Sender.SentByPatient ? practitioner.FullName : patient.FullName, 
            message.Sender.SentByPatient, message.SeenByRecipient));
        }

        return result;
    }
}