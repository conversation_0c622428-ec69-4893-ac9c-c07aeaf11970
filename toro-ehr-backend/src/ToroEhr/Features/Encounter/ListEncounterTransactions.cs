using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Domain;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public sealed record ListEncounterTransactionsQuery(string EncounterId)
    : AuthRequest<List<EncounterTransactionResponse>>;

public sealed record EncounterTransactionResponse(
    string Id,
    DateTime Date,
    decimal Amount,
    string TransactionType,
    string PaymentMethod,
    string CardType,
    string Last4,
    string Status);

internal sealed class
    ListEncounterTransactionsAuth : IAuth<ListEncounterTransactionsQuery, List<EncounterTransactionResponse>>
{
    public ListEncounterTransactionsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class
    ListEncounterTransactionsHandler : IRequestHandler<ListEncounterTransactionsQuery,
    List<EncounterTransactionResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListEncounterTransactionsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<EncounterTransactionResponse>> Handle(ListEncounterTransactionsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Transactions_ByEncounter.Entry encounter = await session
            .Query<Transactions_ByEncounter.Entry, Transactions_ByEncounter>()
            .Include(x => x.PaymentTransactions)
            .Where(x => x.EncounterId == query.EncounterId)
            .ProjectInto<Transactions_ByEncounter.Entry>()
            .FirstOrDefaultAsync(token: cancellationToken);

        List<PaymentTransaction> paymentTransactions =
            (await session.LoadAsync<PaymentTransaction>(encounter.PaymentTransactions, cancellationToken)).Values
            .ToList();

        return paymentTransactions.OrderByDescending(x => x.TransactionDate).Select(x =>
            new EncounterTransactionResponse(x.Id, x.TransactionDate, x.Amount, x.Type, x.PaymentMethod, x.CardType,
                x.Last4, x.Status)).ToList();
    }
}