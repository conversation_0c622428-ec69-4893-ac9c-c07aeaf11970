using FluentValidation;
using MediatR;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Infrastructure;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record UploadFileForEncounterCommunicationCommand(IFormFile Document, string PatientId, string EncounterId)
    : AuthRequest<string>;

internal sealed class UploadFileForEncounterCommunicationAuth : IAuth<UploadFileForEncounterCommunicationCommand, string>
{
    public UploadFileForEncounterCommunicationAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

internal sealed class UploadFileForEncounterCommunicationValidator : AbstractValidator<UploadFileForEncounterCommunicationCommand>
{
    public UploadFileForEncounterCommunicationValidator()
    {
        RuleFor(x => x.PatientId).NotEmpty();
        RuleFor(x => x.EncounterId).NotEmpty();
        RuleFor(x => x.Document).NotEmpty();
    }
}

internal sealed class UploadFileForEncounterCommunicationHandler : IRequestHandler<UploadFileForEncounterCommunicationCommand, string>
{
    private readonly S3FileService _fileService;

    public UploadFileForEncounterCommunicationHandler(S3FileService fileService)
    {
        _fileService = fileService;
    }

    public async Task<string> Handle(UploadFileForEncounterCommunicationCommand command, CancellationToken cancellationToken)
    {
        var filePath = $"patients/{command.PatientId}/encounters/{command.EncounterId}/emails/{command.Document.FileName}";
        await using var stream = command.Document.OpenReadStream();
        await _fileService.UploadFile(stream, command.Document.ContentType, Config.S3.AppFilesBucketName, filePath);

        return Utils.GenerateS3PublicFileUrl(Config.S3.AppFilesBucketName, filePath) ?? string.Empty;
    }
}