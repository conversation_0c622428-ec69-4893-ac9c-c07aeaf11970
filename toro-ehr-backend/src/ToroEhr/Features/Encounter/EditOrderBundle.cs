using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public record EditOrderBundleCommand(string Id, DateTimeOffset? StartTime, string? Note, bool? Fasting, bool? Repeat, string? Priority) : AuthRequest<Unit>;

internal class EditOrderBundleAuth : IAuth<EditOrderBundleCommand, Unit>
{
    public EditOrderBundleAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class EditOrderBundleValidator : AbstractValidator<EditOrderBundleCommand>
{
    public EditOrderBundleValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

internal class EditOrderBundleHandler : IRequestHandler<EditOrderBundleCommand, Unit>
{
    private readonly IDocumentStore _store;

    public EditOrderBundleHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(EditOrderBundleCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.OrderBundle orderBundle = await session.LoadAsync<Domain.OrderBundle>(command.Id);
        Guard.AgainstNotFound(orderBundle, new("OrderBundle.NotFound", $"Order Bundle with 'id:' {command.Id} not found!"));

        foreach (var order in orderBundle.Orders)
        {
            switch (order)
            {
                case OrderMedication om:
                    om.Update(om.MedicationId, om.IngredientId, om.Frequency, om.CustomFrequency, om.Duration, om.Prn,
                        om.PrnReason, command.StartTime ?? om.StartTime, command.Note ?? om.Instructions, om.Name, command.Priority ?? om.Priority);
                    break;
                case OrderLab ol:
                    ol.Update(ol.LoincCodeId, ol.Specimen, command.Note ?? ol.Note, command.Fasting ?? ol.Fasting, command.Repeat ?? ol.Repeat,
                        ol.Name, command.Priority ?? ol.Priority);
                    break;
                case OrderProcedure op:
                    op.Update(op.SnomedCodeId, command.Note ?? op.Note, command.Fasting ?? op.Fasting, command.Repeat ?? op.Repeat,
                        op.Name, command.Priority ?? op.Priority);
                    break;
            }
        }

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}