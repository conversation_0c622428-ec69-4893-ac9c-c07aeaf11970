using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Encounter.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;

namespace ToroEhr.Features.Encounter;

public sealed record VoidOrRefundTotalCommand(string TransactionId) : AuthRequest<ProcessPaymentResponse>;

internal sealed class VoidOrRefundTotalCommandAuth : IAuth<VoidOrRefundTotalCommand, ProcessPaymentResponse>
{
    public VoidOrRefundTotalCommandAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class VoidOrRefundTotalCommandValidator : AbstractValidator<VoidOrRefundTotalCommand>
{
    public VoidOrRefundTotalCommandValidator()
    {
        RuleFor(x => x.TransactionId).NotEmpty();
    }
}

internal sealed class VoidOrRefundTotalHandler : IRequestHandler<VoidOrRefundTotalCommand, ProcessPaymentResponse>
{
    private readonly IDocumentStore _store;
    private readonly IposService _iposService;
    private readonly IposCloudService _iposCloudService;

    public VoidOrRefundTotalHandler(IDocumentStore store, Authenticator authenticator, IposService iposService, IposCloudService iposCloudService)
    {
        _store = store;
        _iposService = iposService;
        _iposCloudService = iposCloudService;
    }

    public async Task<ProcessPaymentResponse> Handle(VoidOrRefundTotalCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        PaymentTransaction paymentTransaction = await session
            .LoadAsync<PaymentTransaction>(command.TransactionId, cancellationToken);

        Guard.AgainstNotFound(paymentTransaction, new("PaymentTransaction.NotFound", $"Payment transaction with id '{command.TransactionId}' not found"));

        // Only process approved transactions
        if (paymentTransaction.Status != TransactionStatus.Approved.Name)
        {
            return new ProcessPaymentResponse(false, $"Cannot void or refund transaction with status: {paymentTransaction.Status}");
        }

        // Load encounter and location to get payment configuration
        Domain.Encounter encounter = await session
            .Include<Domain.Encounter>(x => x.LocationId)
            .LoadAsync<Domain.Encounter>(paymentTransaction.EncounterId, cancellationToken);

        Guard.AgainstNotFound(encounter, new("Encounter.NotFound", $"Encounter with id '{paymentTransaction.EncounterId}' not found"));

        Domain.Location location = await session.LoadAsync<Domain.Location>(encounter.LocationId, cancellationToken);

        if (location.IposPaysConfig == null)
        {
            return new ProcessPaymentResponse(false, "Payment is not enabled for this location");
        }

        // Try void first, then refund if void fails
        bool isVoidSuccessful = false;
        bool isRefundSuccessful = false;
        string errorMessage = "";

        try
        {
            if (paymentTransaction.PaymentMethod == PaymentMethod.PosTerminal.Name)
            {
                isVoidSuccessful = await TryVoidPosTerminalTransaction(paymentTransaction, location.IposPaysConfig);
            }
            else if (paymentTransaction.PaymentMethod == PaymentMethod.SavedCard.Name)
            {
                isVoidSuccessful = await TryVoidSavedCardTransaction(paymentTransaction, location.IposPaysConfig);
            }
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
        }

        // If void failed, try refund
        if (!isVoidSuccessful)
        {
            try
            {
                if (paymentTransaction.PaymentMethod == PaymentMethod.PosTerminal.Name)
                {
                    isRefundSuccessful = await TryRefundPosTerminalTransaction(paymentTransaction, location.IposPaysConfig);
                }
                else if (paymentTransaction.PaymentMethod == PaymentMethod.SavedCard.Name)
                {
                    isRefundSuccessful = await TryRefundSavedCardTransaction(paymentTransaction, location.IposPaysConfig);
                }
            }
            catch (Exception ex)
            {
                errorMessage += $" Refund also failed: {ex.Message}";
            }
        }

        // Update transaction status or create refund transaction
        if (isVoidSuccessful)
        {
            paymentTransaction.VoidTransaction();
            await session.SaveChangesAsync(cancellationToken);
            return new ProcessPaymentResponse(true, "Transaction voided successfully.");
        }
        else if (isRefundSuccessful)
        {
            // Create a new refund transaction
            PaymentTransaction refundTransaction = PaymentTransaction.CreateRefundTransaction(
                paymentTransaction.PatientId, paymentTransaction.EncounterId, paymentTransaction.PaymentMethod,
                paymentTransaction.CardType, paymentTransaction.Last4, paymentTransaction.ReferenceId,
                paymentTransaction.Amount, DateTime.UtcNow, TransactionStatus.Approved.Name, "00");

            await session.StoreAsync(refundTransaction, cancellationToken);
            await session.SaveChangesAsync(cancellationToken);
            return new ProcessPaymentResponse(true, "Transaction refunded successfully.");
        }
        else
        {
            return new ProcessPaymentResponse(false, $"Failed to void or refund transaction. {errorMessage}");
        }
    }

    private async Task<bool> TryVoidPosTerminalTransaction(PaymentTransaction transaction, IposPaysConfig config)
    {
        var voidRequest = new IposVoidRefundRequest(transaction.ReferenceId, config.Tpn, config.AuthKey, transaction.Amount);
        var response = await _iposService.VoidTransactionAsync(voidRequest);
        return response.GeneralResponse.ResultCode == "0";
    }

    private async Task<bool> TryRefundPosTerminalTransaction(PaymentTransaction transaction, IposPaysConfig config)
    {
        var refundRequest = new IposVoidRefundRequest(transaction.ReferenceId, config.Tpn, config.AuthKey, transaction.Amount);
        var response = await _iposService.RefundTransactionAsync(refundRequest);
        return response.GeneralResponse.ResultCode == "0";
    }

    private async Task<bool> TryVoidSavedCardTransaction(PaymentTransaction transaction, IposPaysConfig config)
    {
        if (string.IsNullOrEmpty(transaction.ExternalReference))
        {
            throw new InvalidOperationException("TransactionId is required for IPOS Cloud void operations");
        }

        var voidRequest = new IposCloudVoidRefundRequest(
            new MerchantAuthentication(config.CloudTpn, Utils.GenerateRandomId()),
            VoidRefundTransactionRequest.CreateRefundRequest(transaction.ExternalReference, transaction.Amount));

        var response = await _iposCloudService.VoidTransactionAsync(voidRequest, config.CloudAuthKey);
        return response.Iposhpresponse?.ResponseCode == "200";
    }

    private async Task<bool> TryRefundSavedCardTransaction(PaymentTransaction transaction, IposPaysConfig config)
    {
        if (string.IsNullOrEmpty(transaction.ExternalReference))
        {
            throw new InvalidOperationException("TransactionId is required for IPOS Cloud refund operations");
        }

        var refundRequest = new IposCloudVoidRefundRequest(
            new MerchantAuthentication(config.CloudTpn, Utils.GenerateRandomId()),
            VoidRefundTransactionRequest.CreateRefundRequest(transaction.ExternalReference, transaction.Amount));

        var response = await _iposCloudService.RefundTransactionAsync(refundRequest, config.CloudAuthKey);
        return response.Iposhpresponse?.ResponseCode == "200";
    }
}