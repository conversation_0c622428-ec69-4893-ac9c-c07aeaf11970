using ToroEhr.Infrastructure.ErrorHandling;

namespace ToroEhr.Features.Encounter.Shared;

public static class EncounterErrors
{
    public static AppError NotFoundById(string encounterId) => new("Encounter.NotFound",
        $"The encounter with the  Id: '{encounterId}' was not found");
    public static AppError NotFoundByPatientId(string patientId) => new("Encounter.NotFound",
        $"The encounter with the Patient Id: '{patientId}' was not found");
    public static AppError NotFoundImagingFile(string imagingResultId) => new("ImagingFile.NotFound",
        $"The Imaging File for Imaging Result Id: '{imagingResultId}' was not found");
}