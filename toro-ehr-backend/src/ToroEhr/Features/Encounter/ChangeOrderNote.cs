using FluentValidation;
using MediatR;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Encounter;

public record ChangeOrderNoteCommand(string OrderId, string? Note) : AuthRequest<Unit>;

internal class ChangeOrderNoteAuth : IAuth<ChangeOrderNoteCommand, Unit>
{
    public ChangeOrderNoteAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPractitioner(user);
    }
}

public class ChangeOrderNoteValidator : AbstractValidator<ChangeOrderNoteCommand>
{
    public ChangeOrderNoteValidator()
    {
        RuleFor(x => x.OrderId).NotEmpty();
    }
}

internal class ChangeOrderNoteHandler : IRequestHandler<ChangeOrderNoteCommand, Unit>
{
    private readonly IDocumentStore _store;

    public ChangeOrderNoteHandler(IDocumentStore store)
    {
        _store = store;
    }

    public async Task<Unit> Handle(ChangeOrderNoteCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        OrderMedication orderMedication = await session.LoadAsync<OrderMedication>(command.OrderId, cancellationToken);
        orderMedication.UpdateNote(command.Note ?? string.Empty);

        await session.SaveChangesAsync();

        return Unit.Value;
    }
}