using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Enums;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Patient;

public sealed record ListPatientRecordsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<PatientRecordResponse>>;

public sealed record PatientRecordResponse(
    string Id,
    string PractitionerName,
    string LocationName,
    DateTimeOffset StartAt,
    DateTimeOffset? StartedAt,
    string Status,
    string? ScratchText,
    List<VitalSignResponse> VitalSigns,
    bool HasUnseenMessages);

public sealed record VitalSignResponse(
    DateTimeOffset Date,
    List<MeasurementResponse> Measurements);

public sealed record MeasurementResponse(
    string Type,
    string Value);

internal sealed class ListPatientRecordsAuth : IAuth<ListPatientRecordsQuery, PaginatedList<PatientRecordResponse>>
{
    public ListPatientRecordsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class ListPatientRecordsHandler : IRequestHandler<ListPatientRecordsQuery, PaginatedList<PatientRecordResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientRecordsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<PatientRecordResponse>> Handle(ListPatientRecordsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var entries = await session.Query<Encounter_WithCommunication.Entry, Encounter_WithCommunication>()
            .ProjectInto<Encounter_WithCommunication.Entry>()
            .Include(x => x.PractitionerId)
            .Include(x => x.LocationId)
            .Where(x => x.PatientId == _user.PatientId)
            .Where(x => x.EncounterStatus == EncounterStatus.Completed.Name)
            .OrderByDescending(x => x.StartAt)
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(cancellationToken);

        var encounters = (await session.LoadAsync<Domain.Encounter>(entries.Select(x => x.EncounterId).ToList(), cancellationToken)).Values;

        var practitioners = (await session.LoadAsync<Domain.Employee>(encounters.Select(x => x.PractitionerId).ToList(), cancellationToken)).Values;
        var locations = (await session.LoadAsync<Domain.Location>(encounters.Select(x => x.LocationId).ToList(), cancellationToken)).Values;


        var records = encounters.Select(encounter => new PatientRecordResponse(
            encounter.Id,
            practitioners.First(x => x.Id == encounter.PractitionerId).FullName,
            locations.First(x => x.Id == encounter.LocationId).Name,
            encounter.StartAt,
            encounter.StartedAt,
            encounter.Status,
            encounter.ScratchText,
            [],
            entries.FirstOrDefault(x => x.EncounterId == encounter.Id)?.UnseenByPatient ?? false
        )).ToList();

        return PaginatedList<PatientRecordResponse>.Create(
            records,
            stats.TotalResults,
            query.PagedSearchParams.PageNumber,
            query.PagedSearchParams.PageSize);
    }
}