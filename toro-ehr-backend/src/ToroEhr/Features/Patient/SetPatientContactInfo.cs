using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Services;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Features.Patient;

public sealed record SetPatientContactInfoCommand(
    AddressRequest Address,
    AddressRequest? PreviousAddress,
    IEnumerable<PhoneNumberRequest> PhoneNumbers,
    IEnumerable<EmailAddressRequest> Emails,
    string PreferredContactName,
    string PreferredContactMethod,
    string SocialSecurityNumber,
    IEnumerable<EmergencyContactRequest> EmergencyContacts) : AuthRequest<Unit>;

public record EmergencyContactRequest(string Name, string Relationship, string PhoneNumber, bool Primary);

public record PhoneNumberRequest(string Number, string Type, bool IsPrimary);

public record EmailAddressRequest(string Email, bool IsPrimary);

internal sealed class SetPatientContactInfoAuth : IAuth<SetPatientContactInfoCommand, Unit>
{
    public SetPatientContactInfoAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

public sealed class SetPatientContactInfoCommandValidator : AbstractValidator<SetPatientContactInfoCommand>
{
    public SetPatientContactInfoCommandValidator()
    {

    }
}

internal sealed class SetPatientContactInfoHandler : IRequestHandler<SetPatientContactInfoCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SetPatientContactInfoHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(SetPatientContactInfoCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patient = await session.LoadAsync<Domain.Patient>(_user.PatientId, cancellationToken);

        patient.SetContactInfo(command);

        // separate methods because of tracking changes history
        var newAddress = Domain.Address.Create(command.Address.Street, command.Address.City,
            command.Address.State, command.Address.ZipCode);
        patient.SetAddress(newAddress, command.Timestamp);

        await session.StoreAsync(patient, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}