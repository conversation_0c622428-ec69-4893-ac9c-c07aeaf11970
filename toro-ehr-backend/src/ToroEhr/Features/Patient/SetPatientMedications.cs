using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public sealed record SetPatientMedicationsCommand(IEnumerable<PatientMedicationRequest> Medications)
    : AuthRequest<Unit>;

public record PatientMedicationRequest(string Code, string DisplayName);

internal sealed class SetPatientMedicationsAuth : IAuth<SetPatientPersonalInfoCommand, Unit>
{
    public SetPatientMedicationsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class SetPatientMedicationsCommandValidator : AbstractValidator<SetPatientMedicationsCommand>
{
    public SetPatientMedicationsCommandValidator()
    {
        RuleFor(x => x.Medications)
            .NotEmpty()
            .ForEach(med =>
            {
                med.NotEmpty();
                med.ChildRules(m =>
                {
                    m.RuleFor(med => med.Code).NotEmpty();
                    m.RuleFor(med => med.DisplayName).NotEmpty();
                });
            });
    }
}

internal sealed class SetPatientMedicationsHandler : IRequestHandler<SetPatientMedicationsCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SetPatientMedicationsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(SetPatientMedicationsCommand command, CancellationToken cancellationToken)
    {

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patient = await session.LoadAsync<Domain.Patient>(_user.PatientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(_user.PatientId!));

        patient.SetMedications(command.Medications);

        await session.StoreAsync(patient, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}