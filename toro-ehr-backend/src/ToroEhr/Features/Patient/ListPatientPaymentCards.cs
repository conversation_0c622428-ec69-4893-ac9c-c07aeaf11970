using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public record ListPatientPaymentCardsQuery(string PatientId) : AuthRequest<List<PaymentCardResponse>>;

internal class ListPatientPaymentCardsAuth : IAuth<ListPatientPaymentCardsQuery, List<PaymentCardResponse>>
{
    public ListPatientPaymentCardsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

public record PaymentCardResponse(
    string Id,
    string Type,
    string LastFour,
    string ExpirationDate);

internal class ListPatientPaymentCardsHandler : IRequestHandler<ListPatientPaymentCardsQuery, List<PaymentCardResponse>>
{
    private readonly IDocumentStore _store;

    public ListPatientPaymentCardsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
    }

    public async Task<List<PaymentCardResponse>> Handle(ListPatientPaymentCardsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        Domain.Patient patient = await session.LoadAsync<Domain.Patient>(query.PatientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(query.PatientId));

        List<Domain.PatientPaymentCard> paymentCards = await session.Query<Domain.PatientPaymentCard>()
            .Where(x => x.PatientId == patient.Id)
            .ToListAsync(cancellationToken);

        return paymentCards.Select(x => new PaymentCardResponse(x.Id, x.CardType, x.Last4, x.ExpirationDate))
            .ToList();
    }
}