using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;
using SessionOptions = Raven.Client.Documents.Session.SessionOptions;

namespace ToroEhr.Features.Patient.PatientInsurance;

public sealed record ListPatientInsuranceQuery : AuthRequest<IEnumerable<PatientInsuranceResponse>>;

public sealed record PatientInsuranceResponse(
    string Id,
    string Issuer,
    string GroupId,
    string MemberId,
    DateOnly Start,
    DateOnly? End,
    string Order,
    string Type,
    decimal? Copay,
    decimal? Deductible,
    string Relationship,
    SubscriberResponse? Subscriber,
    string? CardFrontUrl,
    string? CardBackUrl);

public sealed record SubscriberResponse(string FirstName, string LastName, DateOnly Birthday, string BirthSex);

internal sealed class ListPatientInsuranceAuth : IAuth<ListPatientInsuranceQuery, IEnumerable<PatientInsuranceResponse>>
{
    public ListPatientInsuranceAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class
    ListPatientInsuranceHandler : IRequestHandler<ListPatientInsuranceQuery, IEnumerable<PatientInsuranceResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientInsuranceHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<IEnumerable<PatientInsuranceResponse>> Handle(ListPatientInsuranceQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession(new SessionOptions() { NoTracking = true });

        IEnumerable<Domain.PatientInsurance> insurances = await session.Query<Domain.PatientInsurance>()
            .Where(x => x.PatientId == _user.PatientId).ToListAsync(token: cancellationToken);

        return insurances.Select(x => new PatientInsuranceResponse(x.Id, x.Issuer, x.GroupId, x.MemberId, x.Start,
            x.End, x.Order, x.Type, x.Copay, x.Deductible, x.Relationship,
            x.Subscriber != null
                ? new SubscriberResponse(x.Subscriber.FirstName, x.Subscriber.LastName, x.Subscriber.Birthday,
                    x.Subscriber.BirthSex)
                : null, 
            Utils.GenerateS3PublicFileUrl(Config.S3.AppFilesBucketName, x.CardFrontUrl),
            Utils.GenerateS3PublicFileUrl(Config.S3.AppFilesBucketName, x.CardBackUrl)));
    }
}