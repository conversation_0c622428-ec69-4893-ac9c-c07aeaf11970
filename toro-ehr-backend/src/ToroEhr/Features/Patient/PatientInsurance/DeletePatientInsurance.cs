using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient.PatientInsurance;

public sealed record DeletePatientInsuranceCommand(string PatientInsuranceId) : AuthRequest<Unit>;

internal sealed class DeletePatientInsuranceAuth : IAuth<SetPatientPersonalInfoCommand, Unit>
{
    public DeletePatientInsuranceAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class DeletePatientInsuranceCommandValidator : AbstractValidator<DeletePatientInsuranceCommand>
{
    public DeletePatientInsuranceCommandValidator()
    {
        RuleFor(x => x.PatientInsuranceId).NotEmpty();
    }
}

internal sealed class DeletePatientInsuranceHandler : IRequestHandler<DeletePatientInsuranceCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public DeletePatientInsuranceHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(DeletePatientInsuranceCommand command, CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        session.Delete(command.PatientInsuranceId);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}