using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Indexes;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;
using ToroEhr.Shared;

namespace ToroEhr.Features.Patient;

public sealed record ListPatientsQuery(PagedSearchParams PagedSearchParams)
    : AuthRequest<PaginatedList<PatientResponse>>;

internal sealed class ListPatientsAuth : IAuth<ListPatientsQuery, PaginatedList<PatientResponse>>
{
    public ListPatientsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsEmployee(user);
    }
}

internal sealed class ListPatientsHandler : IRequestHandler<ListPatientsQuery, PaginatedList<PatientResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListPatientsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<PaginatedList<PatientResponse>> Handle(ListPatientsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        IRavenQueryable<Patient_ByOrganization.Entry> entries = session
            .Query<Patient_ByOrganization.Entry, Patient_ByOrganization>()
            .Where(x => x.OrganizationId == _user.SelectedOrganizationId)
            .ProjectInto<Patient_ByOrganization.Entry>()
            .Include(x => x.PatientId);

        if (query.PagedSearchParams.SearchParam.IsNotNullOrWhiteSpace())
        {
            entries = entries.Search(x => x.SearchParams, $"{query.PagedSearchParams.SearchParam}*");
        }

        var results = await entries
            .Statistics(out QueryStatistics stats)
            .Skip((query.PagedSearchParams.PageNumber - 1) * query.PagedSearchParams.PageSize)
            .Take(query.PagedSearchParams.PageSize)
            .ToListAsync(token: cancellationToken);

        var patients = new List<PatientResponse>();
        foreach (var entry in results)
        {
            var patient = await session.LoadAsync<Domain.Patient>(entry.PatientId, cancellationToken);
            patients.Add(new PatientResponse(patient.Id, patient.Mrn, patient.Email, patient.FirstName, patient.LastName, patient.Birthday,
                patient.PhoneNumbers.First(x => x.IsPrimary).Number, patient.PreferredContactMethod));
        }

        return PaginatedList<PatientResponse>.Create(
            patients,
            stats.TotalResults,
            query.PagedSearchParams.PageNumber, query.PagedSearchParams.PageSize);
    }
}