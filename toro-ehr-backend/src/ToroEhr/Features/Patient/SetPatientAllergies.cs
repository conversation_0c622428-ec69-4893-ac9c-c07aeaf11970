using FluentValidation;
using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using ToroEhr.Features.Patient.Shared;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public sealed record SetPatientAllergiesCommand(IEnumerable<PatientAllergyRequest> Allergies)
    : AuthRequest<Unit>;

public record PatientAllergyRequest(string Code, string DisplayName, string Reaction, string Severity);

internal sealed class SetPatientAllergiesAuth : IAuth<SetPatientPersonalInfoCommand, Unit>
{
    public SetPatientAllergiesAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class SetPatientAllergiesCommandValidator : AbstractValidator<SetPatientAllergiesCommand>
{
    public SetPatientAllergiesCommandValidator()
    {
        RuleFor(x => x.Allergies)
            .NotEmpty()
            .ForEach(alrgRequest =>
            {
                alrgRequest.NotEmpty();
                alrgRequest.ChildRules(m =>
                {
                    m.RuleFor(alrg => alrg.Code).NotEmpty();
                    m.RuleFor(alrg => alrg.DisplayName).NotEmpty();
                    m.RuleFor(alrg => alrg.Reaction).NotEmpty();
                    m.RuleFor(alrg => alrg.Severity).NotEmpty();
                });
            });
    }
}

internal sealed class SetPatientAllergiesHandler : IRequestHandler<SetPatientAllergiesCommand, Unit>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public SetPatientAllergiesHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<Unit> Handle(SetPatientAllergiesCommand command, CancellationToken cancellationToken)
    {

        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        var patient = await session.LoadAsync<Domain.Patient>(_user.PatientId, cancellationToken);
        Guard.AgainstNotFound(patient, PatientErrors.NotFoundById(_user.PatientId!));

        patient.SetAllergies(command.Allergies);

        await session.StoreAsync(patient, cancellationToken);
        await session.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}