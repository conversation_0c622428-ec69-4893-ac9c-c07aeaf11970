using MediatR;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.Infrastructure.Mediatr.Auth;

namespace ToroEhr.Features.Patient;

public sealed record ListVitalSignsQuery(string PatientId) : AuthRequest<List<VitalSignResponse>>;

internal sealed class ListVitalSignsAuth : IAuth<ListVitalSignsQuery, List<VitalSignResponse>>
{
    public ListVitalSignsAuth(Authenticator authenticator)
    {
        var user = authenticator.User;
        AuthorizationGuard.AffirmIsPatient(user);
    }
}

internal sealed class ListVitalSignsHandler : IRequestHandler<ListVitalSignsQuery, List<VitalSignResponse>>
{
    private readonly IDocumentStore _store;
    private readonly UserRequestSession _user;

    public ListVitalSignsHandler(IDocumentStore store, Authenticator authenticator)
    {
        _store = store;
        _user = authenticator.User;
    }

    public async Task<List<VitalSignResponse>> Handle(ListVitalSignsQuery query,
        CancellationToken cancellationToken)
    {
        using IAsyncDocumentSession session = _store.OpenAsyncSession();

        // show 10 latest
        var encounters = await session.Query<Domain.VitalSign>()
            .Where(x => x.PatientId == query.PatientId)
            .OrderByDescending(x => x.Date)
            .Take(10)
            .ToListAsync(cancellationToken);

        return encounters.Select(encounter => new VitalSignResponse(
            encounter.Date,
            encounter.Measurements.Select(x => new MeasurementResponse(x.Type, x.Value)).ToList())).ToList();
    }
}