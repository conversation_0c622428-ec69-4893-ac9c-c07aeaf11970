using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public sealed class NoteTemplate : Entity
{
    [JsonConstructor]
    private NoteTemplate(string name, string classification, string? specialization,
        string specialityCode, string documentType, List<NoteTemplateField> fields, NamedEntity organization,
        List<NamedEntity> locations, NamedEntity creator)
    {
        Name = name;
        Classification = classification;
        Specialization = specialization;
        DocumentType = documentType;
        SpecialityCode = specialityCode;
        Fields = fields;
        Organization = organization;
        Locations = locations;
        Creator = creator;
    }

    public string Name { get; private set; }

    public string Classification { get; private set; }

    public string? Specialization { get; private set; }

    public string SpecialityCode { get; private set; }

    public string DocumentType { get; private set; }

    public List<NoteTemplateField> Fields { get; private set; }

    public NamedEntity Organization { get; private set; }

    public List<NamedEntity> Locations { get; private set; }
    
    public NamedEntity Creator { get; private set; }

    public static NoteTemplate Create(string name, string classification, string? specialization,
        string documentType, string specialityCode, List<NoteTemplateField> fields, NamedEntity organization,
        List<NamedEntity> locations, NamedEntity creator)
    {
        return new NoteTemplate(name, classification, specialization, documentType, specialityCode, fields,
            organization, locations, creator);
    }
}

public record NoteTemplateField(string Name, string Value, bool IsRequired);