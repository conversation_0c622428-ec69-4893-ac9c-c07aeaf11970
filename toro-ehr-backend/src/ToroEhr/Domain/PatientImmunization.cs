namespace ToroEhr.Domain;

public sealed class PatientImmunization : Entity
{
    public PatientImmunization(string patientId, string immunizationCode, string immunizationDisplayName,
        DateOnly immunizationDate)
    {
        PatientId = patientId;
        ImmunizationCode = immunizationCode;
        ImmunizationDisplayName = immunizationDisplayName;
        ImmunizationDate = immunizationDate;
    }

    public string PatientId { get; private set; }

    public string ImmunizationCode { get; private set; }

    public string ImmunizationDisplayName { get; private set; }

    public DateOnly ImmunizationDate { get; private set; }
    
    public static PatientImmunization Create(string patientId, string immunizationCode, string immunizationDisplayName,
        DateOnly immunizationDate)
    {
        return new PatientImmunization(patientId, immunizationCode, immunizationDisplayName, immunizationDate);
    }
}