namespace ToroEhr.Domain;

public class VitalSign
{
    public VitalSign(string patient, string encounterId, DateTimeOffset date, List<Measurement> measurements)
    {
        PatientId = patient;
        EncounterId = encounterId;
        Date = date;
        Measurements = measurements;
    }

    public string PatientId { get; private set; }

    public string EncounterId { get; private set; }

    public DateTimeOffset Date { get; private set; }

    public List<Measurement> Measurements { get; private set; } = [];

    public static VitalSign Create(string patientId, string encounterId, DateTimeOffset date,
        List<Measurement> measurements) => new(patientId, encounterId, date, measurements);
}

public record Measurement(string Type, string Value);