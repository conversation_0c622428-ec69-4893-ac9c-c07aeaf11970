using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class User : Entity
{
    [JsonConstructor]
    private User(string primaryEmail, string firstName, string lastName, string? patientId, string? employeeId, List<string> userRoles)
    {
        PrimaryEmail = primaryEmail;
        FirstName = firstName;
        LastName = lastName;
        PatientId = patientId;
        EmployeeId = employeeId;
        UserRoles = userRoles;
    }

    public string PrimaryEmail { get; private set; }

    public string FirstName { get; private set; }

    public string LastName { get; private set; }

    public string? Password { get; private set; }

    public List<string> Emails { get; private set; } = [];

    public string? PatientId { get; private set; }

    public string? EmployeeId { get; private set; }

    public List<string> UserRoles { get; private set; }

    public DateTime? LastLogin { get; private set; }

    public void UpdateLastLogin(DateTime lastLogin) => LastLogin = lastLogin;

    public void SetPassword(string password)
    {
        string hashedPassword = BCrypt.Net.BCrypt.HashPassword(password);
        Password = hashedPassword;
    }

    public void SetPatient(string patientId)
    {
        PatientId = patientId;
    }

    public static User Create(string primaryEmail, string firstName, string lastName, string? patientId,
        string? employeeId, List<string> userRoles) => new(primaryEmail, firstName, lastName, patientId, employeeId, userRoles);
}