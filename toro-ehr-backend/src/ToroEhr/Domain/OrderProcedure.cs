using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public class OrderProcedure : Order
{
    [JsonConstructor]
    private OrderProcedure(string encounterId, string patientId, string snomedCodeId,
    string? note, bool fasting, bool repeat, string name, string priority, string status,
        DateTimeOffset createdAt, string createdBy) :
        base(encounterId, patientId, name, priority, status, createdAt, createdBy)
    {
        SnomedCodeId = snomedCodeId;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;
    }
    public string SnomedCodeId { get; private set; }
    public bool Fasting { get; private set; }
    public bool Repeat { get; private set; }
    public string? Note { get; private set; }

    public static OrderProcedure Create(
    string encounterId, string patientId, string snomedCodeId, string note, bool fasting, bool repeat, string name,
    string priority, DateTimeOffset createdAt, string createdBy)
    {
        return new OrderProcedure(encounterId, patientId, snomedCodeId, note, fasting, repeat, name, priority, OrderStatus.Draft,
            createdAt, createdBy);
    }

    public void Update(string snomedCodeId,
    string? note, bool fasting, bool repeat, string name, string priority)
    {
        SnomedCodeId = snomedCodeId;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;

        UpdateName(name);
        UpdatePriority(priority);
    }
}