
using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public class EncounterEmail : CommunicationEntity
{
    [JsonConstructor]
    private EncounterEmail(string patientId, Sender sender, string? encounterId, string subject, string message, DateTimeOffset sentAt, 
        List<string> attachments, string? replyTo) : 
        base(patientId, sender, encounterId, subject, message, sentAt, replyTo)
    {
        Attachments = attachments;
    }
    public List<string> Attachments { get; private set; } = [];

    public static EncounterEmail Create(
    string patientId,
    Sender sender,
    string? encounterId,
    string subject,
    string message,
    DateTimeOffset sentAt,
    List<string> attachments, 
    string? replyTo
)
    {
        return new EncounterEmail(patientId, sender, encounterId, subject, message, sentAt, attachments, replyTo);
    }
}
