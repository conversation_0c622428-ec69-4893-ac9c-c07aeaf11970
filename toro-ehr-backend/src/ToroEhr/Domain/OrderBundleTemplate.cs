using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class OrderBundleTemplate : Entity
{
    [JsonConstructor]
    public OrderBundleTemplate(string locationId, string name, string priority, DateTimeOffset createdAt, string createdBy)
    {
        LocationId = locationId;
        Name = name;
        Priority = priority;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
    }

    public string LocationId { get; private set; }
    public string Name { get; private set; }
    public string Priority { get; private set; }
    public DateTimeOffset CreatedAt { get; private set; }
    public string CreatedBy { get; private set; }

    public List<OrderBundleTemplateStep> Steps { get; private set; } = [];

    public static OrderBundleTemplate Create(string locationId, string name, string priority, DateTimeOffset createdAt, string createdBy)
    {
        return new OrderBundleTemplate(locationId, name, priority, createdAt, createdBy);
    }

    public void Update(string locationId, string name, string priority)
    {
        LocationId = locationId;
        Name = name;
        Priority = priority;
    }

    public void UpdateSteps(List<OrderBundleTemplateStep> steps)
    {
        Steps = steps;
    }
}
