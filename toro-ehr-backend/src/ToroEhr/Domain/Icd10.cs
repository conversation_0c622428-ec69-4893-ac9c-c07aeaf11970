using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Icd10 : CodingEntity
{
    [JsonConstructor]
    private Icd10(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName) :
        base(code, codeSystem, codeSystemName, codeSystemVersion, displayName)
    {
    }

    public static Icd10 Create(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName)
    {
        return new Icd10(code, codeSystem, codeSystemName, codeSystemVersion, displayName);
    }
}