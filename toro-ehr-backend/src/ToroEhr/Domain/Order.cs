namespace ToroEhr.Domain;

public abstract class Order : Entity
{
    protected Order(string? encounterId, string patientId, string name, string priority, string status, DateTimeOffset createdAt, string createdBy)
    {
        EncounterId = encounterId;
        PatientId = patientId;
        Name = name;
        Priority = priority;
        Status = status;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
    }

    public string? EncounterId { get; private set; }
    public string PatientId { get; private set; }
    public string Name { get; private set; }
    public string Priority { get; private set; }
    public string Status { get; private set; }
    public DateTimeOffset CreatedAt { get; private set; }
    public DateTimeOffset? CompletedAt { get; private set; }
    public string CreatedBy { get; private set; }

    public void UpdateName(string name)
    {
        Name = name;
    }

    public void UpdateStatus(string status)
    {
        Status = status;
    }

    public void UpdatePriority(string priority)
    {
        Priority = priority;
    }

    public void UpdateCompletedAt(DateTimeOffset completedAt)
    {
        CompletedAt = completedAt;
    }
}