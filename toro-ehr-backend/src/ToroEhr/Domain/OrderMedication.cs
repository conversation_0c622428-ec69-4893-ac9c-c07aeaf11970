using Newtonsoft.Json;
using ToroEhr.Enums;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public class OrderMedication : Order
{
    [JsonConstructor]
    private OrderMedication(string encounterId, string patientId, string medicationId, string ingredientId, string frequency, string? customFrequency,
        string duration, bool prn, string? prnReason, DateTimeOffset startTime, string instructions, string name, string priority, string status,
        DateTimeOffset createdAt, string createdBy) :
        base(encounterId, patientId, name, priority, status, createdAt, createdBy)
    {
        MedicationId = medicationId;
        IngredientId = ingredientId;
        Frequency = frequency;
        Duration = duration;
        Prn = prn;
        PrnReason = prnReason;
        StartTime = startTime;
        Instructions = instructions;
        CustomFrequency = customFrequency;
    }
    public string MedicationId { get; private set; }
    public string IngredientId { get; private set; }
    public string Frequency { get; private set; }
    public string? CustomFrequency { get; private set; }
    public string Duration { get; private set; }
    public bool Prn { get; private set; }
    public string? PrnReason { get; private set; }
    public DateTimeOffset StartTime { get; private set; }
    public string Instructions { get; private set; }
    public string? TimingStatus { get; private set; }
    public string? Note { get; private set; }

    public List<Administation> Administations { get; private set; } = [];

    public static OrderMedication Create(
    string encounterId, string patientId, string medicationId, string ingredientId, string frequency, string? customFrequency,
        string duration, bool prn, string? prnReason, DateTimeOffset startTime, string instructions, string name, DateTimeOffset createdAt, string createdBy)
    {
        return new OrderMedication(encounterId, patientId, medicationId, ingredientId, frequency, customFrequency, duration, prn, prnReason, startTime,
            instructions, name, OrderPriorityType.Routine, OrderStatus.Draft, createdAt, createdBy);
    }

    public void Update(string medicationId, string ingredientId, string frequency, string? customFrequency, string duration, bool prn, string? prnReason, DateTimeOffset startTime,
        string instructions, string name, string priority)
    {
        MedicationId = medicationId;
        IngredientId = ingredientId;
        Frequency = frequency;
        CustomFrequency = customFrequency;
        Duration = duration;
        Prn = prn;
        PrnReason = prnReason;
        StartTime = startTime;
        Instructions = instructions;
        UpdateName(name);
        UpdatePriority(priority);
    }

    public void UpdateTimingStatus(string timingStatus)
    {
        TimingStatus = timingStatus;
    }

    public void UpdateNote(string note)
    {
        Note = note;
    }

    public void AddToAdministations(Administation administation)
    {
        Administations.Add(administation);
    }
}