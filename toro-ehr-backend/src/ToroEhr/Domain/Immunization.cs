using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Immunization : CodingEntity
{
    [JsonConstructor]
    private Immunization(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName) :
        base(code, codeSystem, codeSystemName, codeSystemVersion, displayName)
    {
    }

    public static Immunization Create(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName)
    {
        return new Immunization(code, codeSystem, codeSystemName, codeSystemVersion, displayName);
    }
}