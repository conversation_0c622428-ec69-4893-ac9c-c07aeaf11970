namespace ToroEhr.Domain;

public abstract class OrderEntry : Entity
{
    protected OrderEntry(string name, bool isRequired/*, DateTimeOffset createdAt, string createdBy*/)
    {
        Name = name;
        IsRequired = isRequired;
        //CreatedAt = createdAt;
        //CreatedBy = createdBy;
    }

    public string Name { get; private set; }
    public bool IsRequired { get; private set; }
    //public DateTimeOffset CreatedAt { get; private set; }
    //public string CreatedBy { get; private set; }

    protected void Update(string name,/* string priority, */bool isRequired) 
    {
        Name = name;
        IsRequired = isRequired;
    }
}