using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class PatientPaymentCard : Entity
{
    [JsonConstructor]
    private PatientPaymentCard(string patientId, string cardType, string last4, string expirationDate, string name,
        string iposToken)
    {
        PatientId = patientId;
        CardType = cardType;
        Last4 = last4;
        ExpirationDate = expirationDate;
        Name = name;
        IposToken = iposToken;
    }

    public string PatientId { get; private set; }

    public string CardType { get; private set; }

    public string Last4 { get; private set; }

    public string ExpirationDate { get; private set; }

    public string Name { get; private set; }

    public string IposToken { get; private set; }

    public static PatientPaymentCard Create(string patientId, string cardType, string last4, string expirationDate,
        string name, string iposToken)
    {
        return new PatientPaymentCard(patientId, cardType, last4, expirationDate, name, iposToken);
    }
}