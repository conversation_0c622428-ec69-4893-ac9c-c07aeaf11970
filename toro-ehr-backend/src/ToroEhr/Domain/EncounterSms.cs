
using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public class EncounterSms : CommunicationEntity
{
    [JsonConstructor]
    private EncounterSms(string patientId, Sender sender, string? encounterId,  string subject, string message, DateTimeOffset sentAt) : 
        base(patientId, sender, encounterId, subject, message, sentAt, null)
    {
    }
    public static EncounterSms Create(string patientId, Sender sender, string? encounterId, string subject, string message, DateTimeOffset sentAt)
    {
        return new EncounterSms(patientId, sender, encounterId, subject, message, sentAt);
    }
}
