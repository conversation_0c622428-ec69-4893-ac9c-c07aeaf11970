using FluentValidation;
using Newtonsoft.Json;
using ToroEhr.Infrastructure.ErrorHandling;

namespace ToroEhr.Domain;

public sealed class Organization : Entity
{
    [JsonConstructor]
    private Organization(string name)
    {
        Name = name;
    }

    public string Name { get; private set; }
    
    private static readonly OrganizationValidator OrgValidator = new();

    public static Organization Create(string name)
    {
        Organization org = new Organization(name);
        return Validator.Validate(org, OrgValidator);
    }

    public void Update(string name) 
    {
        Name = name;
        
        Validator.Validate(this, OrgValidator);
    }
}

public class OrganizationValidator : AbstractValidator<Organization>
{
    public OrganizationValidator()
    {
        RuleFor(x => x.Name).NotEmpty();
    }
}