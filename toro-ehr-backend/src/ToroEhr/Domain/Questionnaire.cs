namespace ToroEhr.Domain;

public sealed class Questionnaire : Entity
{
    public Questionnaire(string locationId, string title, string type, string placement, List<Question> questions)
    {
        LocationId = locationId;
        Title = title;
        Type = type;
        Placement = placement;
        Questions = questions;
    }

    public string LocationId { get; private set; }
    public string Title { get; private set; }

    public string Placement { get; private set; }

    public string Type { get; private set; }

    public List<Question> Questions { get; private set; }

    public static Questionnaire Create(string locationId, string title, string type, string placement,
        List<Question> questions)
    {
        return new Questionnaire(locationId, title, type, placement, questions);
    }

    public void Update(string title, string type, string placement, List<Question> questions)
    {
        Title = title;
        Type = type;
        Placement = placement;
        Questions = questions;
    }
}

public record Question(string Id, string Text, string Type, bool IsRequired = false, IEnumerable<string> Options = null!)
{
    public IEnumerable<string> Options { get; init; } = Options ?? [];
}