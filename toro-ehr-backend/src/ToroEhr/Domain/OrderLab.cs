using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public class OrderLab : Order
{
    [JsonConstructor]
    private OrderLab(string encounterId, string patientId, string loincCodeId, string specimen,
    string note, bool fasting, bool repeat, string name, string priority, string status,
        DateTimeOffset createdAt, string createdBy) :
        base(encounterId, patientId, name, priority, status, createdAt, createdBy)
    {
        LoincCodeId = loincCodeId;
        Specimen = specimen;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;
    }
    public string LoincCodeId { get; private set; }
    public string Specimen { get; private set; }
    public bool Fasting { get; private set; }
    public bool Repeat { get; private set; }
    public string? Note { get; private set; }

    public static OrderLab Create(
    string encounterId, string patientId, string loincCodeId, string specimen, string note, bool fasting, bool repeat, string name,
    string priority, DateTimeOffset createdAt, string createdBy)
    {
        return new OrderLab(encounterId, patientId, loincCodeId, specimen, note, fasting, repeat, name, priority, OrderStatus.Draft,
            createdAt, createdBy);
    }

    public void Update(string loincCodeId, string specimen,
    string? note, bool fasting, bool repeat, string name, string priority)
    {
        LoincCodeId = loincCodeId;
        Specimen = specimen;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;

        UpdateName(name);
        UpdatePriority(priority);
    }
}