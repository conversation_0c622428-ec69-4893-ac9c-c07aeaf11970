using ToroEhr.Enums;

namespace ToroEhr.Domain;

public class OrderBundleTemplateStep
{

    public OrderBundleTemplateStep(int stepNumber, List<OrderEntry> orderEntries)
    {
        StepNumber = stepNumber;
        OrderEntries = orderEntries;
    }

    public int StepNumber { get; private set; }
    public List<OrderEntry> OrderEntries { get; private set; } = [];

    public static OrderBundleTemplateStep Create(int stepNumber)
    {
        return new OrderBundleTemplateStep(stepNumber, new List<OrderEntry>());
    }
}
