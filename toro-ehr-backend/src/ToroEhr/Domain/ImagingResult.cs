using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class ImagingResult
{
    [JsonConstructor]

    private ImagingResult(
    string id,
    string status,
    string patientId,
    string? encounterId,
    DateTime? effectiveDate,
    DateTime? issued,
    string? performerId,
    string reference,
    List<ImagingFile> files)
    {
        Id = id;
        Status = status;
        PatientId = patientId;
        EncounterId = encounterId;
        EffectiveDate = effectiveDate;
        Issued = issued;
        PerformerId = performerId;
        Reference = reference;
        Files = files;
    }

    public string Id { get; private set; }
    public string Status { get; private set; }
    public string PatientId { get; private set; }
    public string? EncounterId { get; private set; }
    public DateTime? EffectiveDate { get; private set; }
    public DateTime? Issued { get; private set; }
    public string? PerformerId { get; private set; } // Reference to Practitioner or Organization
    public string Reference { get; private set; }
    public List<ImagingFile> Files { get; private set; }

    public static ImagingResult Create(
        string id,
        string status,
        string patientId,
        string? encounterId,
        DateTime? effectiveDate,
        DateTime? issued,
        string? performerId,
        string reference,
        List<ImagingFile> files)
    {
        return new ImagingResult(id, status, patientId, encounterId, effectiveDate, issued, performerId, reference, files);
    }
}
