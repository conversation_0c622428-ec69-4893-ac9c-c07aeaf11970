using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class ImagingFile
{
    [JsonConstructor]
    private ImagingFile(string contentType, string? title, string? url)
    {
        ContentType = contentType;
        Title = title;
        Url = url;
    }

    public string ContentType { get; private set; }
    public string? Title { get; private set; }
    public string? Url { get; private set; }
    public bool Seen { get; private set; }



    public static ImagingFile Create(string contentType, string? title = null, string? url = null)
    {
        return new ImagingFile(contentType, title, url);
    }

    public void MarkAsSeen()
    {
        Seen = true;
    }
}