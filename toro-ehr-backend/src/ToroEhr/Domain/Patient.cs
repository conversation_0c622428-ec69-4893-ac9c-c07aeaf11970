using Newtonsoft.Json;
using ToroEhr.Features.Patient;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public sealed class Patient : Entity
{
    [JsonConstructor]
    private Patient(string mrn, string email, string firstName, string lastName, DateTime birthday, string phoneNumber)
    {
        Mrn = mrn;
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        Birthday = birthday;
        PhoneNumbers = [PhoneNumber.Create(phoneNumber, "Home", true)];
        Emails = [EmailAddress.Create(email, true)];
    }

    public string Mrn { get; private set; }
    public string Email { get; private set; }

    public string FirstName { get; private set; }

    public string LastName { get; private set; }

    public string? MiddleName { get; private set; }

    public string? Suffix { get; private set; }

    public string? PreferredName { get; private set; }

    public string? PreviousFirstName { get; private set; }

    public string? PreviousLastName { get; private set; }

    public string? PreferredLanguage { get; private set; }

    public DateTime Birthday { get; private set; }

    public string? BirthSex { get; private set; }

    public string? GenderIdentity { get; private set; }

    public string? SexualOrientation { get; private set; }

    public string? Race { get; private set; }

    public string? Ethnicity { get; private set; }

    public string? TribalAffiliation { get; private set; }

    public Address? Address { get; private set; }

    public Address? PreviousAddress { get; private set; }

    public List<PhoneNumber> PhoneNumbers { get; private set; }

    public List<EmailAddress> Emails { get; private set; } = [];

    public string? PreferredContactMethod { get; private set; }

    public string? PreferredContactName { get; private set; }

    public string? SocialSecurityNumber { get; private set; }

    public List<EmergencyContact> EmergencyContacts { get; private set; } = [];
    public List<FirstNameHistoryEntry> FirstNameHistory { get; private set; } = [];

    public List<LastNameHistoryEntry> LastNameHistory { get; private set; } = [];

    public List<AddressHistoryEntry> AddressHistory { get; private set; } = [];

    public List<PatientDocument> Documents { get; private set; } = [];

    public List<PatientMedication> Medications { get; private set; } = [];

    public List<PatientAllergy> Allergies { get; private set; } = [];

    public void SetPersonalInfo(SetPatientPersonalInfoCommand command)
    {
        MiddleName = command.MiddleName;
        Suffix = command.Suffix;
        PreferredName = command.PreferredName;
        PreviousFirstName = command.PreviousFirstName;
        PreviousLastName = command.PreviousLastName;
        PreferredLanguage = command.PreferredLanguage;
        Birthday = command.Birthday;
        BirthSex = command.BirthSex;
        GenderIdentity = command.GenderIdentity;
        SexualOrientation = command.SexualOrientation;
        Race = command.Race;
        Ethnicity = command.Ethnicity;
        TribalAffiliation = command.TribalAffiliation;
    }

    public void SetFirstName(string newFirstName, DateTime modifiedAt)
    {
        if (newFirstName == FirstName)
        {
            return;
        }

        var index = FirstNameHistory.FindIndex(x => x.ValidTo == null);
        if (index != -1)
        {
            FirstNameHistory[index] = FirstNameHistory[index] with { ValidTo = modifiedAt };
        }

        FirstNameHistory.Add(new FirstNameHistoryEntry(newFirstName, modifiedAt, null));
        FirstName = newFirstName;
    }

    public void SetLastName(string newLastName, DateTime modifiedAt)
    {
        if (newLastName == LastName)
        {
            return;
        }

        var index = LastNameHistory.FindIndex(x => x.ValidTo == null);
        if (index != -1)
        {
            LastNameHistory[index] = LastNameHistory[index] with { ValidTo = modifiedAt };
        }

        LastNameHistory.Add(new LastNameHistoryEntry(newLastName, modifiedAt, null));
        LastName = newLastName;
    }

    public void AddDocument(string docId, string documentType, List<string> filePaths, DateTime createdAt)
    {
        Documents.Add(new PatientDocument(docId, documentType, filePaths, createdAt));
    }

    public void DeleteDocuments(IEnumerable<string> documentsIds)
    {
        Documents.RemoveAll(x => documentsIds.Contains(x.Id));
    }

    public void SetMedications(IEnumerable<PatientMedicationRequest> medications)
    {
        Medications = medications.Select(x => new PatientMedication(x.Code, x.DisplayName)).ToList();
    }

    public void SetAllergies(IEnumerable<PatientAllergyRequest> allergies)
    {
        Allergies = allergies.Select(x => new PatientAllergy(x.Code, x.DisplayName, x.Reaction, x.Severity)).ToList();
    }

    public void SetMrn(string mrn)
    {
        if (string.IsNullOrWhiteSpace(mrn))
            throw new ArgumentException("MRN cannot be null or empty", nameof(mrn));

        Mrn = mrn;
    }

    public static Patient Create(string mrn, string email, string firstName, string lastName, DateTime birthday,
        string phoneNumber) => new(mrn, email, firstName, lastName, birthday, phoneNumber);

    [JsonIgnore]
    public string FullName => $"{FirstName} {LastName}";

    public void SetContactInfo(SetPatientContactInfoCommand command)
    {
        Address = Address.Create(command.Address.Street, command.Address.City, command.Address.State,
            command.Address.ZipCode);
        PhoneNumbers = command.PhoneNumbers.Select(x => PhoneNumber.Create(x.Number, x.Type, x.IsPrimary)).ToList();
        Emails = command.Emails.Select(x => EmailAddress.Create(x.Email, x.IsPrimary)).ToList();
        PreferredContactMethod = command.PreferredContactMethod;
        PreferredContactName = command.PreferredContactName;
        SocialSecurityNumber = command.SocialSecurityNumber;
        EmergencyContacts = command.EmergencyContacts
            .Select(x => new EmergencyContact(x.Name, x.Relationship, x.PhoneNumber, x.Primary)).ToList();
    }
}

public record EmergencyContact(string Name, string Relationship, string PhoneNumber, bool Primary);

public record FirstNameHistoryEntry(string Value, DateTime ValidFrom, DateTime? ValidTo);

public record LastNameHistoryEntry(string Value, DateTime ValidFrom, DateTime? ValidTo);

public record PatientDocument(string Id, string Type, List<string> FilePaths, DateTime CreatedAt);

public record PatientMedication(string Code, string DisplayName);

public record PatientAllergy(string Code, string DisplayName, string Reaction, string Severity);