using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public class MedicationOrderEntry : OrderEntry
{
    [JsonConstructor]
    private MedicationOrderEntry(string medicationId, string ingredientId, string frequency, string? customFrequency,
        string duration, bool prn, string? prnReason, string name, bool isRequired/*, DateTimeOffset createdAt, string createdBy*/) :
        base(name, isRequired/*, createdAt, createdBy*/)
    {
        MedicationId = medicationId;
        IngredientId = ingredientId;
        Frequency = frequency;
        Duration = duration;
        Prn = prn;
        PrnReason = prnReason;
        CustomFrequency = customFrequency;
    }
    public string MedicationId { get; private set; }
    public string IngredientId { get; private set; }
    public string Frequency { get; private set; }
    public string? CustomFrequency { get; private set; }
    public string Duration { get; private set; }
    public bool Prn { get; private set; }
    public string? PrnReason { get; private set; }

    public static MedicationOrderEntry Create(
    string medicationId, string ingredientId, string frequency, string? customFrequency, string duration, bool prn, string? prnReason, 
    string name, bool isRequired/*, DateTimeOffset createdAt, string createdBy*/)
    {
        return new MedicationOrderEntry(medicationId, ingredientId, frequency, customFrequency, duration, prn, prnReason,
            name, isRequired/*, createdAt, createdBy*/);
    }

    public void Update(string medicationId, string ingredientId, string frequency, string? customFrequency, string duration, 
        bool prn, string? prnReason, string name, bool isRequired)
    {
        MedicationId = medicationId;
        IngredientId = ingredientId;
        Frequency = frequency;
        CustomFrequency = customFrequency;
        Duration = duration;
        Prn = prn;
        PrnReason = prnReason;
        Update(name, isRequired);
    }
}