using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public sealed class Medication
{
    [JsonConstructor]
    private Medication(
    string rxCui,
    string language,
    string termStatus,
    string lexicalUniqueId,
    string stringType,
    string stringUniqueId,
    bool isPreferred,
    string atomUniqueId,
    string sourceAtomId,
    string sourceConceptId,
    string sourceDescriptorId,
    string sourceAbbreviation,
    string termType,
    string sourceCode,
    string termString,
    string sourceRestrictionLevel,
    string suppress,
    string contentViewFlag)
    {
        Id = $"{nameof(Medication).Substring(0, 3)}-{atomUniqueId}";
        RxCui = rxCui;
        Language = language;
        TermStatus = termStatus;
        LexicalUniqueId = lexicalUniqueId;
        StringType = stringType;
        StringUniqueId = stringUniqueId;
        IsPreferred = isPreferred;
        AtomUniqueId = atomUniqueId;
        SourceAtomId = sourceAtomId;
        SourceConceptId = sourceConceptId;
        SourceDescriptorId = sourceDescriptorId;
        SourceAbbreviation = sourceAbbreviation;
        TermType = termType;
        SourceCode = sourceCode;
        TermString = termString;
        SourceRestrictionLevel = sourceRestrictionLevel;
        Suppress = suppress;
        ContentViewFlag = contentViewFlag;
    }

    public string Id { get; private set; } = string.Empty;
    public string RxCui { get; private set; }               // RXCUI
    public string Language { get; private set; }            // LAT
    public string TermStatus { get; private set; }          // TS
    public string LexicalUniqueId { get; private set; }     // LUI
    public string StringType { get; private set; }          // STT
    public string StringUniqueId { get; private set; }      // SUI
    public bool IsPreferred { get; private set; }           // ISPREF
    public string AtomUniqueId { get; private set; }        // RXAUI
    public string SourceAtomId { get; private set; }        // SAUI
    public string SourceConceptId { get; private set; }     // SCUI
    public string SourceDescriptorId { get; private set; }  // SDUI
    public string SourceAbbreviation { get; private set; }  // SAB
    public string TermType { get; private set; }            // TTY
    public string SourceCode { get; private set; }          // CODE
    public string TermString { get; private set; }          // STR
    public string SourceRestrictionLevel { get; private set; } // SRL
    public string Suppress { get; private set; }            // SUPPRESS
    public string ContentViewFlag { get; private set; }     // CVF

    public static Medication Create(
        string rxCui,
        string language,
        string termStatus,
        string lexicalUniqueId,
        string stringType,
        string stringUniqueId,
        bool isPreferred,
        string atomUniqueId,
        string sourceAtomId,
        string sourceConceptId,
        string sourceDescriptorId,
        string sourceAbbreviation,
        string termType,
        string sourceCode,
        string termString,
        string sourceRestrictionLevel,
        string suppress,
        string contentViewFlag)
    {
        return new Medication(
            rxCui,
            language,
            termStatus,
            lexicalUniqueId,
            stringType,
            stringUniqueId,
            isPreferred,
            atomUniqueId,
            sourceAtomId,
            sourceConceptId,
            sourceDescriptorId,
            sourceAbbreviation,
            termType,
            sourceCode,
            termString,
            sourceRestrictionLevel,
            suppress,
            contentViewFlag);
    }
}