using Newtonsoft.Json;
using ToroEhr.Features.LoincCode;

namespace ToroEhr.Domain;

public class LoincCode
{
    [JsonConstructor]
    private LoincCode(
        string? loincNum, string? component, string? property, string? timeAspct, string? system,
        string? scaleTyp, string? methodTyp, string? @class, string? versionLastChanged, string? chngType,
        string? definitionDescription, string? status, string? consumerName, string? classtype, string? formula,
        string? exmplAnswers, string? surveyQuestText, string? surveyQuestSrc, string? unitsRequired,
        string? relatedNames2, string? shortName, string? orderObs, string? hl7FieldSubfieldId,
        string? externalCopyrightNotice, string? exampleUnits, string? longCommonName, string? exampleUcumUnits,
        string? statusReason, string? statusText, string? changeReasonPublic, int? commonTestRank,
        int? commonOrderRank, string? hl7AttachmentStructure, string? externalCopyrightLink, string? panelType,
        string? askAtOrderEntry, string? associatedObservations, string? versionFirstReleased,
        string? validHl7AttachmentRequest, string? displayName)
    {
        Id = $"Loi-{loincNum}";
        LoincNum = loincNum;
        Component = component;
        Property = property;
        TimeAspct = timeAspct;
        System = system;
        ScaleTyp = scaleTyp;
        MethodTyp = methodTyp;
        Class = @class;
        VersionLastChanged = versionLastChanged;
        ChngType = chngType;
        DefinitionDescription = definitionDescription;
        Status = status;
        ConsumerName = consumerName;
        Classtype = classtype;
        Formula = formula;
        ExmplAnswers = exmplAnswers;
        SurveyQuestText = surveyQuestText;
        SurveyQuestSrc = surveyQuestSrc;
        UnitsRequired = unitsRequired == "Y";
        RelatedNames2 = relatedNames2;
        ShortName = shortName;
        OrderObs = orderObs;
        Hl7FieldSubfieldId = hl7FieldSubfieldId;
        ExternalCopyrightNotice = externalCopyrightNotice;
        ExampleUnits = exampleUnits;
        LongCommonName = longCommonName;
        ExampleUcumUnits = exampleUcumUnits;
        StatusReason = statusReason;
        StatusText = statusText;
        ChangeReasonPublic = changeReasonPublic;
        CommonTestRank = commonTestRank;
        CommonOrderRank = commonOrderRank;
        Hl7AttachmentStructure = hl7AttachmentStructure;
        ExternalCopyrightLink = externalCopyrightLink;
        PanelType = panelType;
        AskAtOrderEntry = askAtOrderEntry;
        AssociatedObservations = associatedObservations;
        VersionFirstReleased = versionFirstReleased;
        ValidHl7AttachmentRequest = validHl7AttachmentRequest == "Y";
        DisplayName = displayName;
    }

    public string Id { get; private set; }
    public string? LoincNum { get; private set; }
    public string? Component { get; private set; }
    public string? Property { get; private set; }
    public string? TimeAspct { get; private set; }
    public string? System { get; private set; }
    public string? ScaleTyp { get; private set; }
    public string? MethodTyp { get; private set; }
    public string? Class { get; private set; }
    public string? VersionLastChanged { get; private set; }
    public string? ChngType { get; private set; }
    public string? DefinitionDescription { get; private set; }
    public string? Status { get; private set; }
    public string? ConsumerName { get; private set; }
    public string? Classtype { get; private set; }
    public string? Formula { get; private set; }
    public string? ExmplAnswers { get; private set; }
    public string? SurveyQuestText { get; private set; }
    public string? SurveyQuestSrc { get; private set; }
    public bool UnitsRequired { get; private set; }
    public string? RelatedNames2 { get; private set; }
    public string? ShortName { get; private set; }
    public string? OrderObs { get; private set; }
    public string? Hl7FieldSubfieldId { get; private set; }
    public string? ExternalCopyrightNotice { get; private set; }
    public string? ExampleUnits { get; private set; }
    public string? LongCommonName { get; private set; }
    public string? ExampleUcumUnits { get; private set; }
    public string? StatusReason { get; private set; }
    public string? StatusText { get; private set; }
    public string? ChangeReasonPublic { get; private set; }
    public int? CommonTestRank { get; private set; }
    public int? CommonOrderRank { get; private set; }
    public string? Hl7AttachmentStructure { get; private set; }
    public string? ExternalCopyrightLink { get; private set; }
    public string? PanelType { get; private set; }
    public string? AskAtOrderEntry { get; private set; }
    public string? AssociatedObservations { get; private set; }
    public string? VersionFirstReleased { get; private set; }
    public bool ValidHl7AttachmentRequest { get; private set; }
    public string? DisplayName { get; private set; }

    public static LoincCode Create(LoincEntry loincEntry)
    {
        return new LoincCode(
            loincEntry?.LoincNum, loincEntry?.Component, loincEntry?.Property, loincEntry?.TimeAspct, loincEntry?.System, loincEntry?.ScaleTyp,
            loincEntry?.MethodTyp, loincEntry?.Class, loincEntry?.VersionLastChanged, loincEntry?.ChngType, loincEntry?.DefinitionDescription,
            loincEntry?.Status, loincEntry?.ConsumerName, loincEntry?.Classtype, loincEntry?.Formula,
            loincEntry?.ExmplAnswers, loincEntry?.SurveyQuestText, loincEntry?.SurveyQuestSrc, loincEntry?.UnitsRequired, loincEntry?.RelatedNames2,
            loincEntry?.ShortName, loincEntry?.OrderObs, loincEntry?.Hl7FieldSubfieldId, loincEntry?.ExternalCopyrightNotice, loincEntry?.ExampleUnits,
            loincEntry?.LongCommonName, loincEntry?.ExampleUcumUnits, loincEntry?.StatusReason, loincEntry?.StatusText, loincEntry?.ChangeReasonPublic,
            loincEntry?.CommonTestRank, loincEntry?.CommonOrderRank, loincEntry?.Hl7AttachmentStructure, loincEntry?.ExternalCopyrightLink,
            loincEntry?.PanelType, loincEntry?.AskAtOrderEntry, loincEntry?.AssociatedObservations, loincEntry?.VersionFirstReleased, 
            loincEntry?.ValidHl7AttachmentRequest, loincEntry?.DisplayName
        );
    }
}
