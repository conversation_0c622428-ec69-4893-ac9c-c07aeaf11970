using Newtonsoft.Json;

namespace ToroEhr.Domain
{
    public class BundleOrderEntry : OrderEntry
    {
        [JsonConstructor]
        public BundleOrderEntry(string bundleId, List<OrderEntry> orderEntries, string name/*, 
            DateTimeOffset createdAt, string createdBy*/) : base(name, false/*, createdAt, createdBy*/)
        {
            BundleId = bundleId;
            OrderEntries = orderEntries;
        }

        public string BundleId { get; private set; }

        public List<OrderEntry> OrderEntries { get; set; }

        public static BundleOrderEntry Create(string bundleId, List<OrderEntry> orderEntries, string name/*,
            DateTimeOffset createdAt, string createdBy*/)
        {
            return new BundleOrderEntry(bundleId, orderEntries, name/*, createdAt, createdBy*/);
        }
    }
}
