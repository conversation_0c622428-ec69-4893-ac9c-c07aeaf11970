using Newtonsoft.Json;
using ToroEhr.Enums;

namespace ToroEhr.Domain;

public sealed class PaymentTransaction : Entity
{
    [JsonConstructor]
    private PaymentTransaction(string patientId, string encounterId, string type, string paymentMethod, string cardType,
        string last4, string referenceId, decimal amount, DateTime transactionDate, string status,
        string paymentStatusCode, string? failReason, string? externalReference, string? rrn)
    {
        PatientId = patientId;
        EncounterId = encounterId;
        Type = type;
        PaymentMethod = paymentMethod;
        CardType = cardType;
        Last4 = last4;
        ReferenceId = referenceId;
        Amount = amount;
        TransactionDate = transactionDate;
        Status = status;
        PaymentStatusCode = paymentStatusCode;
        FailReason = failReason;
        ExternalReference = externalReference;
        Rrn = rrn;
    }

    public string PatientId { get; private set; }

    public string EncounterId { get; private set; }

    public string Type { get; private set; }

    public string PaymentMethod { get; private set; }

    public string CardType { get; private set; }

    public string Last4 { get; private set; }

    public string ReferenceId { get; private set; }

    public decimal Amount { get; private set; }

    public string? ExternalReference { get; private set; }

    public string Status { get; private set; }

    public string PaymentStatusCode { get; private set; }

    public string? FailReason { get; private set; }

    public DateTime TransactionDate { get; private set; }

    public string? Rrn { get; private set; }

    public static PaymentTransaction CreateSuccessTransaction(string patientId, string encounterId, string type,
        string paymentMethod, string cardType, string last4, string referenceId, decimal amount,
        DateTime transactionDate, string status, string paymentStatusCode, string? externalReference = null,
        string? rrn = null)
        => new(patientId, encounterId, type, paymentMethod, cardType, last4, referenceId, amount, transactionDate,
            status, paymentStatusCode, null, externalReference, rrn);

    public static PaymentTransaction CreateFailedTransaction(string patientId, string encounterId, string type,
        string paymentMethod, string cardType, string last4, string referenceId, decimal amount,
        DateTime transactionDate, string status, string paymentStatusCode, string failReason,
        string? transactionId = null, string? rrn = null)
        => new(patientId, encounterId, type, paymentMethod, cardType, last4, referenceId, amount, transactionDate,
            status, paymentStatusCode, failReason, transactionId, rrn);

    public void VoidTransaction()
    {
        Status = TransactionStatus.Voided.Name;
    }

    public void RefundTransaction()
    {
        Status = TransactionStatus.Refunded.Name;
    }

    public static PaymentTransaction CreateRefundTransaction(string patientId, string encounterId, string paymentMethod,
        string cardType, string last4, string originalReferenceId, decimal amount, DateTime transactionDate,
        string status, string paymentStatusCode, string? externalReference = null, string? rrn = null)
    {
        string refundReferenceId = $"refund#{originalReferenceId}";
        var transaction = new PaymentTransaction(patientId, encounterId, TransactionType.Refund.Name, paymentMethod,
            cardType, last4, refundReferenceId, amount, transactionDate, status, paymentStatusCode, null,
            externalReference, rrn);
        return transaction;
    }
}