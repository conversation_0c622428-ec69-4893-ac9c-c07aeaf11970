using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class ProcedureOrderEntry : OrderEntry
{
    [JsonConstructor]
    private ProcedureOrderEntry(string snomedCodeId, string note, string name, bool fasting, bool repeat, bool isRequired) :
        base(name, isRequired)
    {
        SnomedCodeId = snomedCodeId;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;
    }

    public string SnomedCodeId { get; private set; }
    public string Note { get; private set; }
    public bool Fasting { get; private set; }
    public bool Repeat { get; private set; }

    public static ProcedureOrderEntry Create(
    string snomedCodeId, string note, string name, bool fasting, bool repeat, bool isRequired)
    {
        return new ProcedureOrderEntry(snomedCodeId, note, name, fasting, repeat, isRequired);
    }

    public void Update(string snomedCodeId, string note, string name, bool fasting, bool repeat, bool isRequired)
    {
        SnomedCodeId = snomedCodeId;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;
        Update(name, isRequired);
    }
}