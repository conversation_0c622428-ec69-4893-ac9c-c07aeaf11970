using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class UserSession : Entity
{
    [JsonConstructor]
    private UserSession(string userId, string? organizationId, string? locationId, string userRole, string accessToken, 
        string refreshToken, DateTime accessTokenExpiresAt, DateTime refreshTokenExpiresAt, DateTime createdAt)
    {
        UserId = userId;
        OrganizationId = organizationId;
        LocationId = locationId;
        UserRole = userRole;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        AccessTokenExpiresAt = accessTokenExpiresAt;
        RefreshTokenExpiresAt = refreshTokenExpiresAt;
        CreatedAt = createdAt;
    }

    public string UserId { get; private set; }

    public string? OrganizationId { get; private set; }

    public string? LocationId { get; private set; }

    public string UserRole { get; private set; }

    public string AccessToken { get; private set; }

    public string RefreshToken { get; private set; }

    public DateTime AccessTokenExpiresAt { get; private set; }

    public DateTime RefreshTokenExpiresAt { get; private set; }

    public DateTime CreatedAt { get; private set; }

    public static UserSession Create(string userId, string? organizationId, string? locationId, string userRole, string accessToken, string refreshToken,
        DateTime accessTokenExpiresAt, DateTime refreshTokenExpiresAt, DateTime createdAt)
    {
        return new UserSession(userId, organizationId, locationId, userRole, accessToken, refreshToken, accessTokenExpiresAt,
            refreshTokenExpiresAt, createdAt);
    }

    public void Update(string userRole, string organizationId, string locationId)
    {
        UserRole = userRole;
        OrganizationId = organizationId;
        LocationId = locationId;
    }
}