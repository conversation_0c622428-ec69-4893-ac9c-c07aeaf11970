namespace ToroEhr.Domain;

public sealed class PatientInsurance : Entity
{
    public PatientInsurance(string patientId, string issuer, string groupId, string memberId, DateOnly start,
        DateOnly? end, string order, string type, decimal? copay, decimal? deductible, string relationship,
        Subscriber? subscriber, string? cardFrontUrl, string? cardBackUrl)
    {
        PatientId = patientId;
        Issuer = issuer;
        GroupId = groupId;
        MemberId = memberId;
        Start = start;
        End = end;
        Order = order;
        Type = type;
        Copay = copay;
        Deductible = deductible;
        Relationship = relationship;
        Subscriber = subscriber;
        CardFrontUrl = cardFrontUrl;
        CardBackUrl = cardBackUrl;
    }

    public string PatientId { get; private set; }

    public string Issuer { get; private set; }

    public string GroupId { get; private set; }

    public string MemberId { get; private set; }

    public DateOnly Start { get; private set; }

    public DateOnly? End { get; private set; }

    public string Order { get; private set; }

    public string Type { get; private set; }

    public decimal? Copay { get; private set; }

    public decimal? Deductible { get; private set; }

    public string Relationship { get; private set; }

    public Subscriber? Subscriber { get; private set; }

    public string? CardFrontUrl { get; private set; }

    public string? CardBackUrl { get; private set; }

    public static PatientInsurance Create(string patientId, string issuer, string groupId, string memberId,
        DateOnly start, DateOnly? end, string order, string type, decimal? copay, decimal? deductible, string relationship,
        Subscriber? subscriber, string? cardFrontUrl, string? cardBackUrl)
    {
        return new PatientInsurance(patientId, issuer, groupId, memberId, start, end, order, type, copay, deductible,
            relationship, subscriber, cardFrontUrl, cardBackUrl);
    }
}

public record Subscriber(string FirstName, string LastName, DateOnly Birthday, string BirthSex);