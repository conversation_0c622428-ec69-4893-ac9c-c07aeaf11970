using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class OrganizationPatient : Entity
{
    [JsonConstructor]
    private OrganizationPatient(string organizationId, string patientId, string? invitationToken, DateTime invitedAt,
        DateTime? acceptedAt)
    {
        OrganizationId = organizationId;
        PatientId = patientId;
        InvitationToken = invitationToken;
        InvitedAt = invitedAt;
        InviteAcceptedAt = acceptedAt;
    }

    public string OrganizationId { get; private set; }

    public string PatientId { get; private set; }

    public DateTime InvitedAt { get; private set; }

    public string? InvitationToken { get; private set; }

    public DateTime? InviteAcceptedAt { get; private set; }

    public void AcceptInvitation(DateTime acceptedAt)
    {
        InviteAcceptedAt = acceptedAt;
        InvitationToken = null;
    }

    public static OrganizationPatient Create(string organizationId, string patientId, string invitationToken,
        DateTime invitedAt)
    {
        return new OrganizationPatient(organizationId, patientId, invitationToken, invitedAt, null);
    }

    public static OrganizationPatient CreateAndAccept(string organizationId, string patientId, DateTime invitedAt)
    {
        return new OrganizationPatient(organizationId, patientId, null, invitedAt, invitedAt);
    }
}