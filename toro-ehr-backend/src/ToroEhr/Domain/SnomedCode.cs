using Newtonsoft.Json;

namespace ToroEhr.Domain
{
    public sealed class SnomedCode
    {
        [JsonConstructor]
        private SnomedCode(
        string snomedId,
        string efectiveTime,
        bool isActive,
        string moduleId,
        string conceptId,
        string languageId,
        string typeId,
        string term,
        string caseSignificanceId,
        bool isProcedure)
        {
            Id = $"{nameof(SnomedCode).Substring(0, 3)}-{snomedId}";
            SnomedId = snomedId;
            EfectiveTime = efectiveTime;
            IsActive = isActive;
            ModuleId = moduleId;
            ConceptId = conceptId;
            LanguageId = languageId;
            TypeId = typeId;
            Term = term;
            CaseSignificanceId = caseSignificanceId;
            IsProcedure = isProcedure;
        }

        public string Id { get; private set; } = string.Empty;
        public string SnomedId { get; private set; } = string.Empty;
        public string EfectiveTime { get; private set; } = string.Empty;
        public bool IsActive { get; private set; }
        public string ModuleId { get; private set; } = string.Empty;
        public string ConceptId { get; private set; } = string.Empty;
        public string LanguageId { get; private set; } = string.Empty;
        public string TypeId { get; private set; } = string.Empty;
        public string Term { get; private set; } = string.Empty;
        public string CaseSignificanceId { get; private set; } = string.Empty;
        public bool IsProcedure { get; private set; }

        public static SnomedCode Create(
        string snomedId,
        string efectiveTime,
        bool isActive,
        string moduleId,
        string conceptId,
        string languageId,
        string typeId,
        string term,
        string caseSignificanceId,
        bool isProcedure)
        {
            return new SnomedCode(snomedId, efectiveTime, isActive, moduleId, conceptId, languageId, typeId, term, caseSignificanceId, isProcedure);
        }
    }
}
