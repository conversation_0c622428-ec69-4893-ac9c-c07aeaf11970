using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class MedicationRelation
{
    [JsonConstructor]
    private MedicationRelation(
    string rxNormConceptId1,
    string rxNormAtomId1,
    string sourceType1,
    string relationship,
    string rxNormConceptId2,
    string rxNormAtomId2,
    string sourceType2,
    string relationshipAttribute,
    string relationshipUniqueId,
    string sourceRelationshipUniqueId,
    string sourceAbbreviation,
    string sourceLabel,
    string directionality,
    string relationshipGroup,
    string suppress,
    string contentViewFlag)
    {
        Id = $"rel-{relationshipUniqueId}";
        RxNormConceptId1 = rxNormConceptId1;
        RxNormAtomId1 = rxNormAtomId1;
        SourceType1 = sourceType1;
        Relationship = relationship;
        RxNormConceptId2 = rxNormConceptId2;
        RxNormAtomId2 = rxNormAtomId2;
        SourceType2 = sourceType2;
        RelationshipAttribute = relationshipAttribute;
        RelationshipUniqueId = relationshipUniqueId;
        SourceRelationshipUniqueId = sourceRelationshipUniqueId;
        SourceAbbreviation = sourceAbbreviation;
        SourceLabel = sourceLabel;
        Directionality = directionality;
        RelationshipGroup = relationshipGroup;
        Suppress = suppress;
        ContentViewFlag = contentViewFlag;
    }
    public string Id { get; private set; } = string.Empty;
    public string RxNormConceptId1 { get; private set; }  // RXCUI1
    public string RxNormAtomId1 { get; private set; }      // RXAUI1
    public string SourceType1 { get; private set; }        // STYPE1
    public string Relationship { get; private set; }       // REL
    public string RxNormConceptId2 { get; private set; }   // RXCUI2
    public string RxNormAtomId2 { get; private set; }      // RXAUI2
    public string SourceType2 { get; private set; }        // STYPE2
    public string RelationshipAttribute { get; private set; } // RELA
    public string RelationshipUniqueId { get; private set; }  // RUI
    public string SourceRelationshipUniqueId { get; private set; } // SRUI
    public string SourceAbbreviation { get; private set; }  // SAB
    public string SourceLabel { get; private set; }         // SL
    public string Directionality { get; private set; }      // DIR
    public string RelationshipGroup { get; private set; }   // RG
    public string Suppress { get; private set; }        // SUPPRESS
    public string ContentViewFlag { get; private set; }     // CVF

    public static MedicationRelation Create(
        string rxNormConceptId1,
        string rxNormAtomId1,
        string sourceType1,
        string relationship,
        string rxNormConceptId2,
        string rxNormAtomId2,
        string sourceType2,
        string relationshipAttribute,
        string relationshipUniqueId,
        string sourceRelationshipUniqueId,
        string sourceAbbreviation,
        string sourceLabel,
        string directionality,
        string relationshipGroup,
        string suppress,
        string contentViewFlag)
    {
        return new MedicationRelation(
            rxNormConceptId1,
            rxNormAtomId1,
            sourceType1,
            relationship,
            rxNormConceptId2,
            rxNormAtomId2,
            sourceType2,
            relationshipAttribute,
            relationshipUniqueId,
            sourceRelationshipUniqueId,
            sourceAbbreviation,
            sourceLabel,
            directionality,
            relationshipGroup,
            suppress,
            contentViewFlag);
    }
}