using Newtonsoft.Json;
using ToroEhr.Features.Questionnaire;

namespace ToroEhr.Domain;

public sealed class QuestionnaireResponse : Entity
{
    [JsonConstructor]
    private QuestionnaireResponse(string patientId, string questionnaireId, string? encounterId, DateTime submittedAt,
        List<Answer> answers)
    {
        PatientId = patientId;
        QuestionnaireId = questionnaireId;
        SubmittedAt = submittedAt;
        Answers = answers;
    }

    public string PatientId { get; private set; }

    public string QuestionnaireId { get; private set; }

    public string? EncounterId { get; private set; }

    public DateTime SubmittedAt { get; private set; }

    public List<Answer> Answers { get; private set; }

    public static QuestionnaireResponse Create(string patientId, string questionnaireId, string? encounterId,
        DateTime submittedAt, List<Answer> answers) =>
        new(patientId, questionnaireId, encounterId, submittedAt, answers);

    public void Update(IEnumerable<QuestionAnswer> answers, DateTime submittedAt)
    {
        Answers = answers.Select(x => new Answer(x.QuestionId, x.Answers)).ToList();
        SubmittedAt = submittedAt;
    }
}

public sealed record Answer(string QuestionId, List<string> Values);