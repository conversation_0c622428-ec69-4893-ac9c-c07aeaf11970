using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class LaboratoryResult : Entity
{
    [JsonConstructor]
    public LaboratoryResult(string status, string? codeSystem, string? code, string? codeDisplay, string patientId,
        string? encounterId, DateTimeOffset? effectiveDateTime, DateTimeOffset? issued, string? performerId, string? valueQuantity,
        string? valueUnit, string? interpretationCode, string? interpretationDisplay, string? referenceRangeLow,
        string? referenceRangeHigh, string? referenceRangeUnit, string? comments, bool seen)
    {
        Status = status;
        CodeSystem = codeSystem;
        Code = code;
        CodeDisplay = codeDisplay;
        PatientId = patientId;
        EncounterId = encounterId;
        EffectiveDateTime = effectiveDateTime;
        Issued = issued;
        PerformerId = performerId;
        ValueQuantity = valueQuantity;
        ValueUnit = valueUnit;
        InterpretationCode = interpretationCode;
        InterpretationDisplay = interpretationDisplay;
        ReferenceRangeLow = referenceRangeLow;
        ReferenceRangeHigh = referenceRangeHigh;
        ReferenceRangeUnit = referenceRangeUnit;
        Comments = comments;
        Seen = seen;
    }
    // Core Fields
    public string Status { get; private set; } // e.g., 'final'

    // Code representing the lab test (e.g., LOINC)
    public string? CodeSystem { get; private set; } // e.g., "http://loinc.org"
    public string? Code { get; private set; } // e.g., "718-7"
    public string? CodeDisplay { get; private set; } // e.g., "Hemoglobin [Mass/volume] in Blood"

    // Subject (Patient)
    public string PatientId { get; private set; } // Reference to Patient table

    // Encounter
    public string? EncounterId { get; private set; } // Reference to Encounter table

    // Effective date/time
    public DateTimeOffset? EffectiveDateTime { get; private set; }

    // Issued timestamp
    public DateTimeOffset? Issued { get; private set; }

    // Performer(s)
    public string? PerformerId { get; private set; } // Reference to Practitioner or Organization

    // Value (result)
    public string? ValueQuantity { get; private set; } // numeric value
    public string? ValueUnit { get; private set; } // e.g., "g/dL"

    // Interpretation (e.g., high, low)
    public string? InterpretationCode { get; private set; } // e.g., "H" for high
    public string? InterpretationDisplay { get; private set; } // e.g., "High"

    // Reference range (optional)
    public string? ReferenceRangeLow { get; private set; }
    public string? ReferenceRangeHigh { get; private set; }
    public string? ReferenceRangeUnit { get; private set; }

    // Notes / Comments
    public string? Comments { get; private set; }
    public bool Seen { get; private set; }

    public static LaboratoryResult Create(string status, string? codeSystem, string? code, string? codeDisplay, string patientId, string? encounterId,
    DateTimeOffset? effectiveDateTime, DateTimeOffset? issued,  string? performerId, string? valueQuantity,  string? valueUnit, string? interpretationCode,
    string? interpretationDisplay, string? referenceRangeLow, string? referenceRangeHigh, string? referenceRangeUnit, string? comments, bool seen)
    {
        return new LaboratoryResult(
            status, codeSystem, code, codeDisplay, patientId, encounterId, effectiveDateTime, issued, performerId, valueQuantity, valueUnit,
            interpretationCode, interpretationDisplay, referenceRangeLow, referenceRangeHigh, referenceRangeUnit, comments, seen);
    }

    public void MarkAsSeen()
    {
        Seen = true;
    }
}
