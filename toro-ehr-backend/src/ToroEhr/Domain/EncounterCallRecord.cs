
using Newtonsoft.Json;
using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public class EncounterCallRecord : CommunicationEntity
{
    [JsonConstructor]
    private EncounterCallRecord(string patientId, Sender sender, string? encounterId, string subject, string message, DateTimeOffset sentAt) : 
        base(patientId, sender, encounterId, subject, message, sentAt, null)
    {
    }
    public static EncounterCallRecord Create(string patientId, Sender sender, string? encounterId, string subject, string message, DateTimeOffset sentAt)
    {
        return new EncounterCallRecord(patientId, sender, encounterId, subject, message, sentAt);
    }
}
