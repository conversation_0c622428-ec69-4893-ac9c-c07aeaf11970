namespace ToroEhr.Domain;

public abstract class CodingEntity
{
    protected CodingEntity(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName)
    {
        Id = $"{GetType().Name.Substring(0, 3)}-{code}";
        Code = code;
        CodeSystem = codeSystem;
        CodeSystemName = codeSystemName;
        CodeSystemVersion = codeSystemVersion;
        DisplayName = displayName;
    }

    public string Id { get; private set; } = string.Empty;
    public string Code { get; private set; } = string.Empty;
    public string CodeSystem { get; private set; } = string.Empty;
    public string CodeSystemName { get; private set; } = string.Empty;
    public string CodeSystemVersion { get; private set; } = string.Empty;
    public string DisplayName { get; private set; } = string.Empty;

}