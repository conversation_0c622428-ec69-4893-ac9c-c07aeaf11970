using Newtonsoft.Json;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace ToroEhr.Domain;

public sealed class Address
{
    [JsonConstructor]
    private Address(string street, string city, string state, string zipCode)
    {
        Street = street;
        City = city;
        State = state;
        ZipCode = zipCode;
    }

    public string Street { get; private set; }

    public string City { get; private set; }

    public string State { get; private set; }

    public string ZipCode { get; private set; }

    public static Address Create(string street, string city, string state, string zipCode) =>
        new(street, city, state, zipCode);

    public void Update(AddressRequest request)
    {
        Street = request.Street;
        City = request.City;
        State = request.State;
        ZipCode = request.ZipCode;
    }

    public string Formatted =>
        string.Join(", ", new[] { Street, City, State, ZipCode }.Where(s => s.IsNotNullOrWhiteSpace()));
}