using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class Allergie : CodingEntity
{
    [JsonConstructor]
    private Allergie(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName) :
        base(code, codeSystem, codeSystemName, codeSystemVersion, displayName)
    {
    }

    public static Allergie Create(string code, string codeSystem, string codeSystemName, string codeSystemVersion, string displayName)
    {
        return new Allergie(code, codeSystem, codeSystemName, codeSystemVersion, displayName);
    }
}