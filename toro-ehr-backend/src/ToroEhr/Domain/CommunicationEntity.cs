using ToroEhr.ValueObjects;

namespace ToroEhr.Domain;

public abstract class CommunicationEntity : Entity
{
    protected CommunicationEntity(string patientId, Sender sender, string? encounterId, string subject, string message, DateTimeOffset sentAt, string? replyTo)
    {
        PatientId = patientId;
        Sender = sender;
        EncounterId = encounterId;
        Subject = subject;
        Message = message;
        SentAt = sentAt;
        ReplyTo = replyTo;
    }

    public string PatientId { get; private set; }
    public Sender Sender { get; private set; }
    public string? EncounterId { get; private set; }
    public string Subject { get; private set; } = string.Empty;
    public string Message { get; private set; } = string.Empty;
    public DateTimeOffset SentAt { get; private set; }
    public string? ReplyTo { get; private set; }
    public bool SeenByRecipient { get; private set; }

    public void MarkAsSeen()
    {
        SeenByRecipient = true;
    }
}
