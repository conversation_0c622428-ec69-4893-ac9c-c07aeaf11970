using Newtonsoft.Json;

namespace ToroEhr.Domain;

public class LabOrderEntry : OrderEntry
{
    [JsonConstructor]
    private LabOrderEntry(string loincCodeId, string specimen, string note, string name, bool fasting, bool repeat, bool isRequired) :
        base(name, isRequired)
    {
        LoincCodeId = loincCodeId;
        Specimen = specimen;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;
    }

    public string LoincCodeId { get; private set; }
    public string Specimen { get; private set; }
    public string Note { get; private set; }
    public bool Fasting { get; private set; }
    public bool Repeat { get; private set; }

    public static LabOrderEntry Create(
    string loincCodeId, string specimen, string note, string name, bool fasting, bool repeat, bool isRequired)
    {
        return new LabOrderEntry(loincCodeId, specimen, note, name, fasting, repeat, isRequired);
    }

    public void Update(string loincCodeId, string specimen, string note, string name, bool fasting, bool repeat, bool isRequired)
    {
        LoincCodeId = loincCodeId;
        Specimen = specimen;
        Note = note;
        Fasting = fasting;
        Repeat = repeat;
        Update(name, isRequired);
    }
}