using Newtonsoft.Json;

namespace ToroEhr.Domain
{
    public class MedicationAttribute : Entity
    {
        [JsonConstructor]
        private MedicationAttribute(
            string rxNormConceptId,
            string lexicalUniqueId,
            string stringUniqueId,
            string rxNormAtomId,
            string sourceType,
            string sourceCode,
            string attributeUniqueId,
            string sourceAttributeUniqueId,
            string attributeName,
            string sourceAbbreviation,
            string attributeValue,
            string suppressFlag,
            string contentViewFlag)
        {
            RxNormConceptId = rxNormConceptId;
            LexicalUniqueId = lexicalUniqueId;
            StringUniqueId = stringUniqueId;
            RxNormAtomId = rxNormAtomId;
            SourceType = sourceType;
            SourceCode = sourceCode;
            AttributeUniqueId = attributeUniqueId;
            SourceAttributeUniqueId = sourceAttributeUniqueId;
            AttributeName = attributeName;
            SourceAbbreviation = sourceAbbreviation;
            AttributeValue = attributeValue;
            SuppressFlag = suppressFlag;
            ContentViewFlag = contentViewFlag;
        }
        public string RxNormConceptId { get; private set; }         // RXCUI
        public string LexicalUniqueId { get; private set; }         // LUI
        public string StringUniqueId { get; private set; }          // SUI
        public string RxNormAtomId { get; private set; }            // RXAUI
        public string SourceType { get; private set; }              // STYPE
        public string SourceCode { get; private set; }              // CODE
        public string AttributeUniqueId { get; private set; }       // ATUI
        public string SourceAttributeUniqueId { get; private set; } // SATUI
        public string AttributeName { get; private set; }           // ATN
        public string SourceAbbreviation { get; private set; }      // SAB
        public string AttributeValue { get; private set; }          // ATV
        public string SuppressFlag { get; private set; }            // SUPPRESS
        public string ContentViewFlag { get; private set; }         // CVF

        public static MedicationAttribute Create(
            string rxNormConceptId,
            string lexicalUniqueId,
            string stringUniqueId,
            string rxNormAtomId,
            string sourceType,
            string sourceCode,
            string attributeUniqueId,
            string sourceAttributeUniqueId,
            string attributeName,
            string sourceAbbreviation,
            string attributeValue,
            string suppressFlag,
            string contentViewFlag)
        {
            return new MedicationAttribute(
                rxNormConceptId,
                lexicalUniqueId,
                stringUniqueId,
                rxNormAtomId,
                sourceType,
                sourceCode,
                attributeUniqueId,
                sourceAttributeUniqueId,
                attributeName,
                sourceAbbreviation,
                attributeValue,
                suppressFlag,
                contentViewFlag
            );
        }
    }
}
