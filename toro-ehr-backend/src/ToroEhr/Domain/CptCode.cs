using Newtonsoft.Json;

namespace ToroEhr.Domain;

public sealed class CptCode
{
    [JsonConstructor]
    private CptCode(string code, string conceptId, string clinicianDescriptor)
    {
        Id = $"{GetType().Name.Substring(0, 3)}-{code}";
        Code = code;
        ConceptId = conceptId;
        ClinicianDescriptor = clinicianDescriptor;
    }

    public string Id { get; private set; } = string.Empty;
    public string Code { get; private set; } = string.Empty;
    public string ConceptId { get; private set; } = string.Empty;
    public string ClinicianDescriptor { get; private set; } = string.Empty;


    public static CptCode Create(string code, string conceptId, string clinicianDescriptor)
    {
        return new CptCode(code, conceptId, clinicianDescriptor);
    }
}