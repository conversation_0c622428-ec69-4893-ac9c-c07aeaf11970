using System.Text;
using Newtonsoft.Json;

namespace ToroEhr.Services;

public sealed class IposService
{
    private readonly HttpClient _httpClient;

    public IposService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IposTransactionResponse> InitiatePosTransactionAsync(
        IposTransactionRequest iposTransactionRequest)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, "Payment/Sale")
        {
            Content = new StringContent(JsonConvert.SerializeObject(iposTransactionRequest), Encoding.UTF8,
                "application/json")
        };

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<IposTransactionResponse>(responseContent)!;
    }

    public async Task<IposVoidRefundResponse> VoidTransactionAsync(IposVoidRefundRequest voidRequest)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, "Payment/Void")
        {
            Content = new StringContent(JsonConvert.SerializeObject(voidRequest), Encoding.UTF8,
                "application/json")
        };

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<IposVoidRefundResponse>(responseContent)!;
    }

    public async Task<IposVoidRefundResponse> RefundTransactionAsync(IposVoidRefundRequest refundRequest)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, "Payment/Return")
        {
            Content = new StringContent(JsonConvert.SerializeObject(refundRequest), Encoding.UTF8,
                "application/json")
        };

        var response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<IposVoidRefundResponse>(responseContent)!;
    }
}

public record IposTransactionRequest(double Amount, string ReferenceId, string Tpn, string AuthKey)
{
    public string PaymentType { get; init; } = "Credit";
}

public class IposTransactionResponse
{
    public Amounts Amounts { get; set; } = null!;
    public GeneralResponse GeneralResponse { get; set; } = null!;
    public string ReferenceId { get; set; } = null!;
    public string IPosToken { get; set; } = null!;
    public CardData CardData { get; set; } = null!;
    public string? RRN { get; set; }
    public string? TransactionNumber { get; set; }
    public string? BatchNumber { get; set; }
    public string? AuthCode { get; set; }
    public Dictionary<string, ExtendedData> ExtendedDataByApplication { get; set; }
}

public class CardData
{
    public string CardType { get; set; } = null!;
    public string Last4 { get; set; } = null!;
    public string ExpirationDate { get; set; } = null!;
    public string Name { get; set; } = null!;
}

public class Amounts
{
    public decimal TotalAmount { get; set; }
}

public class GeneralResponse
{
    public string ResultCode { get; set; } = null!;
    public string StatusCode { get; set; } = null!;
    public string Message { get; set; } = null!;
    public string DetailedMessage { get; set; } = null!;
}

public record IposVoidRefundRequest(string ReferenceId, string Tpn, string AuthKey, decimal Amount)
{
    public string PaymentType { get; init; } = "Credit";
}

public class IposVoidRefundResponse
{
    public GeneralResponse GeneralResponse { get; set; } = null!;
    public string ReferenceId { get; set; } = null!;
    public decimal Amount { get; set; }
    public string? RRN { get; set; }
    public string? TransactionNumber { get; set; }
    public string? BatchNumber { get; set; }
    public string? AuthCode { get; set; }
    public Dictionary<string, ExtendedData> ExtendedDataByApplication { get; set; }
}



public class ExtendedData
{
    public string? TxnId { get; set; }
}