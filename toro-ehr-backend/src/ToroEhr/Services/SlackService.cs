using Slack.Webhooks;

namespace ToroEhr.Services;

public class SlackService
{
    //TODO: move url to env file
    private const string _webHookUrl = "*********************************************************************************";
    private readonly SlackClient _slackClient;

    public SlackService()
    {
        _slackClient = new SlackClient(_webHookUrl);
    }

    public async Task<bool> SendMessageAsync(string message, string? username = "ToroHealthBot")
    {
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Message cannot be empty", nameof(message));

        var slackMessage = new SlackMessage
        {
            Text = message,
            Username = username,
            IconEmoji = ":robot_face:"
        };

        return await Task.Run(() => _slackClient.Post(slackMessage));
    }
}
