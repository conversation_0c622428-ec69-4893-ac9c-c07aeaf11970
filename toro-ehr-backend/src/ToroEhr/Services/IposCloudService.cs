using System.Text;
using Newtonsoft.Json;

namespace ToroEhr.Services;

public sealed class IposCloudService
{
    private readonly HttpClient _httpClient;

    public IposCloudService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<IposCloudTransactionResponse> InitiatePosCloudTransactionAsync(
        IposCloudTransactionRequest iposCloudTransactionRequest, string authToken)
    {
        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(JsonConvert.SerializeObject(iposCloudTransactionRequest), Encoding.UTF8,
                "application/json"),
            Headers = { { "token", authToken } }
        };

        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<IposCloudTransactionResponse>(responseContent)!;
    }

    public async Task<IposCloudTransactionResponse> VoidTransactionAsync(
        IposCloudVoidRefundRequest voidRequest, string authToken)
    {
        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(JsonConvert.SerializeObject(voidRequest), Encoding.UTF8,
                "application/json"),
            Headers = { { "token", authToken } }
        };

        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<IposCloudTransactionResponse>(responseContent)!;
    }

    public async Task<IposCloudTransactionResponse> RefundTransactionAsync(
        IposCloudVoidRefundRequest refundRequest, string authToken)
    {
        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(JsonConvert.SerializeObject(refundRequest), Encoding.UTF8,
                "application/json"),
            Headers = { { "token", authToken } }
        };

        var response = await _httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<IposCloudTransactionResponse>(responseContent)!;
    }
}

public record IposCloudTransactionRequest(
    [property: JsonProperty("merchantAuthentication")]
    MerchantAuthentication MerchantAuthentication,
    [property: JsonProperty("transactionRequest")] TransactionRequest TransactionRequest);

public record MerchantAuthentication(
    [property: JsonProperty("merchantId")] string MerchantId,
    [property: JsonProperty("transactionReferenceId")] string TransactionReferenceId);

public record TransactionRequest(
    [property: JsonProperty("transactionType")] int TransactionType,
    [property: JsonProperty("amount")] int Amount,
    [property: JsonProperty("cardToken")] string CardToken);

public record IposCloudVoidRefundRequest(
    [property: JsonProperty("merchantAuthentication")]
    MerchantAuthentication MerchantAuthentication,
    [property: JsonProperty("transactionRequest")] VoidRefundTransactionRequest TransactionRequest);

public record VoidRefundTransactionRequest(
    [property: JsonProperty("transactionType")] int TransactionType,
    [property: JsonProperty("amount")] string Amount,
    [property: JsonProperty("achTransactionId")] string AchTransactionId)
{
    public static VoidRefundTransactionRequest CreateRefundRequest(string achTransactionId, decimal customAmount) =>
        new(11, Math.Round(customAmount * 100).ToString("0"), achTransactionId);
};

public class IposCloudTransactionResponse
{
    public SuccessResponse? Iposhpresponse { get; set; }
    public IList<ErrorResponse> Errors { get; set; } = new List<ErrorResponse>();
}

public class SuccessResponse
{
    public string ResponseCode { get; set; } = null!;
    public string ResponseMessage { get; set; } = null!;
    public string TransactionReferenceId { get; set; } = null!;
    public decimal TotalAmount { get; set; }
    public string? TransactionId { get; set; }
    public string? Rrn { get; set; }
}

public class ErrorResponse
{
    public string Field { get; set; } = null!;
    public string Message { get; set; } = null!;
}