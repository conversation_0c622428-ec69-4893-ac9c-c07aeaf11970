using ClosedXML.Excel;
using System.IO.Compression;
using ToroEhr.Domain;
using ToroEhr.Infrastructure.Guards;
using ToroEhr.ValueObjects;

namespace ToroEhr.Services
{
    public class CodeService
    {
        public static async Task<List<CodingRecord>> ParseCodingExcelFile(IFormFile file)
        {
            var records = new List<CodingRecord>();
            var sheetName = "Expansion List";

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0; // Reset stream position

                using (var workbook = new XLWorkbook(stream))
                {
                    var worksheet = workbook.Worksheet(sheetName);
                    if (worksheet == null)
                    {
                        throw new Exception($"Sheet '{sheetName}' not found in the Excel file.");
                    }

                    int startRow = 15; // Start reading from row 15

                    foreach (var row in worksheet.RowsUsed().Skip(startRow - 1))
                    {
                        var data = new CodingRecord
                        {
                            Code = row.Cell(1).GetValue<string>(),  // Column A
                            DisplayName = row.Cell(2).GetValue<string>(),     // Column B
                            CodeSystemName = row.Cell(3).GetValue<string>(), // Column C
                            CodeSystemVersion = row.Cell(4).GetValue<string>(), // Column D
                            CodeSystem = row.Cell(4).GetValue<string>(), // Column E
                        };

                        records.Add(data);
                    }
                }
            }

            return records;
        }

        public static async Task<(List<Medication>, List<MedicationRelation>, List<MedicationAttribute>)> ParseRxNormZipAsync(IFormFile zipFile)
        {
            var medications = new List<Medication>();
            var relationships = new List<MedicationRelation>();

            using var zipStream = zipFile.OpenReadStream();
            using var archive = new ZipArchive(zipStream, ZipArchiveMode.Read);

            // Parse RXNCONSO file
            medications = await ParseRxnConso(archive);

            // Parse RXNREL file
            //relationships = await ParseRxnRel(archive, medications);

            // Parse RXNSAT file
            var atributes = await ParseRxnSat(archive, medications);

            return (medications, relationships, atributes);
        }

        // Private method to parse RXNCONSO.RRF file
        private static async Task<List<Medication>> ParseRxnConso(ZipArchive archive)
        {
            var result = new List<Medication>();
            var entry = archive.GetEntry("rrf/RXNCONSO.RRF");
            Guard.AgainstNotFound(entry, new("File.NotFound", $"RXNCONSO.RRF not found in the zip archive."));

            using var entryStream = entry.Open();
            using var reader = new StreamReader(entryStream);

            while (!reader.EndOfStream)
            {
                var line = await reader.ReadLineAsync();
                if (string.IsNullOrWhiteSpace(line))
                    continue;

                var fields = line.Split('|');
                if (fields.Length < 18)
                    continue;

                var medication = Medication.Create(
                    rxCui: fields[0],
                    language: fields[1],
                    termStatus: fields[2],
                    lexicalUniqueId: fields[3],
                    stringType: fields[4],
                    stringUniqueId: fields[5],
                    isPreferred: fields[6] == "Y",
                    atomUniqueId: fields[7],
                    sourceAtomId: fields[8],
                    sourceConceptId: fields[9],
                    sourceDescriptorId: fields[10],
                    sourceAbbreviation: fields[11],
                    termType: fields[12],
                    sourceCode: fields[13],
                    termString: fields[14],
                    sourceRestrictionLevel: fields[15],
                    suppress: fields[16],
                    contentViewFlag: fields[17]
                );

                if (medication.TermType == "IN" || medication.TermType == "MIN" || medication.TermType == "SCD" || medication.TermType == "GPCK")
                {
                    result.Add(medication);
                }                
            }

            return result;
        }

        // Private method to parse RXNREL.RRF file
        private static async Task<List<MedicationRelation>> ParseRxnRel(ZipArchive archive)
        {
            var result = new List<MedicationRelation>();
            var entry = archive.GetEntry("rrf/RXNREL.RRF");
            Guard.AgainstNotFound(entry, new("File.NotFound", $"RXNREL.RRF not found in the zip archive."));

            using var entryStream = entry.Open();
            using var reader = new StreamReader(entryStream);

            while (!reader.EndOfStream)
            {
                var line = await reader.ReadLineAsync();
                if (string.IsNullOrWhiteSpace(line))
                    continue;

                var fields = line.Split('|');
                if (fields.Length < 16)
                    continue;

                //var relationshipAttribute = fields[7];
                //if (relationshipAttribute != "ingredient_of")
                //    continue;

                var relationship = MedicationRelation.Create(
                    rxNormConceptId1: fields[0],
                    rxNormAtomId1: fields[1],
                    sourceType1: fields[2],
                    relationship: fields[3],
                    rxNormConceptId2: fields[4],
                    rxNormAtomId2: fields[5],
                    sourceType2: fields[6],
                    relationshipAttribute: fields[7],
                    relationshipUniqueId: fields[8],
                    sourceRelationshipUniqueId: fields[9],
                    sourceAbbreviation: fields[10],
                    sourceLabel: fields[11],
                    directionality: fields[12],
                    relationshipGroup: fields[13],
                    suppress: fields[14],
                    contentViewFlag: fields[15]
                );

                result.Add(relationship);
            }

            return result;
        }

        private static async Task<List<MedicationAttribute>> ParseRxnSat(ZipArchive archive, List<Medication> medications)
        {
            var result = new List<MedicationAttribute>();

            var entry = archive.GetEntry("rrf/RXNSAT.RRF");
            Guard.AgainstNotFound(entry, new("File.NotFound", $"RXNSAT.RRF not found in the zip archive."));

            // Convert to HashSet for O(1) lookups
            var rxCuiSet = new HashSet<string>(medications.Select(x => x.RxCui));

            using var entryStream = entry.Open();
            using var reader = new StreamReader(entryStream);

            while (!reader.EndOfStream)
            {
                var line = await reader.ReadLineAsync();
                if (string.IsNullOrWhiteSpace(line))
                    continue;

                var fields = line.Split('|');
                if (fields.Length < 13)
                    continue;

                // Only check RXCUI and ATN before creating the object
                var rxCui = fields[0];
                var atn = fields[8];

                if (!rxCuiSet.Contains(rxCui))
                    continue;

                if (atn != "DST" && atn != "DRT")
                    continue;

                // Only now create the MedicationAttribute
                var attribute = MedicationAttribute.Create(
                    rxNormConceptId: rxCui,
                    lexicalUniqueId: fields[1],
                    stringUniqueId: fields[2],
                    rxNormAtomId: fields[3],
                    sourceType: fields[4],
                    sourceCode: fields[5],
                    attributeUniqueId: fields[6],
                    sourceAttributeUniqueId: fields[7],
                    attributeName: atn,
                    sourceAbbreviation: fields[9],
                    attributeValue: fields[10],
                    suppressFlag: fields[11],
                    contentViewFlag: fields[12]
                );

                result.Add(attribute);
            }

            return result;
        }

        public static async Task<List<SnomedCode>> ParseSctDescription(IFormFile file)
        {
            var result = new List<SnomedCode>();

            using var entryStream = file.OpenReadStream();
            using var reader = new StreamReader(entryStream);

            string? line;
            bool isFirstLine = true;

            while ((line = await reader.ReadLineAsync()) != null)
            {
                if (isFirstLine)
                {
                    isFirstLine = false; // Skip header
                    continue;
                }

                var parts = line.Split('\t');
                if (parts.Length != 9)
                    continue;

                var code = SnomedCode.Create(
                    snomedId: parts[0],
                    efectiveTime: parts[1],
                    isActive: parts[2] == "1",
                    moduleId: parts[3],
                    conceptId: parts[4],
                    languageId: parts[5],
                    typeId: parts[6],
                    term: parts[7],
                    caseSignificanceId: parts[8],
                    isProcedure: parts[7].Contains("(procedure)")
                );

                if (code.Term != null && code.IsActive && code.IsProcedure)
                {
                    result.Add(code);
                }
            }

            return result;
        }
    }
}