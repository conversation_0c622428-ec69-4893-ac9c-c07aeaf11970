using Amazon.S3;
using Amazon.S3.Model;
using ToroEhr.Shared;

namespace ToroEhr.Services;

public class S3FileService
{
    private readonly IAmazonS3 _s3Client;

    public S3FileService(IAmazonS3 s3Client)
    {
        _s3Client = s3Client;
    }

    public async Task UploadFile(Stream stream, string contentType, string bucketName, string filePath)
    {
        var putRequest = new PutObjectRequest
        {
            BucketName = bucketName,
            Key = filePath,
            InputStream = stream,
            ContentType = contentType,
            AutoCloseStream = true,
            DisablePayloadSigning = true
        };

        await _s3Client.PutObjectAsync(putRequest);
    }

    public async Task DeleteFiles(IEnumerable<string> files, string bucketName)
    {
        DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest()
        {
            BucketName = bucketName,
            Objects = files.Select(x => new KeyVersion { Key = x }).ToList()
        };

        await _s3Client.DeleteObjectsAsync(deleteRequest);
    }

    public async Task<Stream> DownloadFile(string bucketName, string filePath)
    {
        var request = new GetObjectRequest
        {
            BucketName = bucketName,
            Key = filePath
        };

        using GetObjectResponse response = await _s3Client.GetObjectAsync(request);
        using MemoryStream ms = new MemoryStream();

        await response.ResponseStream.CopyToAsync(ms);
        return ms;
    }
}