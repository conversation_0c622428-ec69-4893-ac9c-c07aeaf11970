using ToroEhr.Infrastructure;
using Twilio;
using Twilio.Rest.Api.V2010.Account;

namespace ToroEhr.Services;

public class SmsService
{
    public async Task SendSmsAsync(string to, string message)
    {
        if (string.IsNullOrEmpty(to) || string.IsNullOrEmpty(message))
            throw new ArgumentException("Recipient number and message must be provided.");

        TwilioClient.Init(Config.Twilio.TwilioAccountSid, Config.Twilio.TwilioAuthToken);

        var messageResponse = await MessageResource.CreateAsync(
            body: message,
            from: new Twilio.Types.PhoneNumber(Config.Twilio.TwilioPhoneNumber),
            to: new Twilio.Types.PhoneNumber(to)
        );
    }
}
