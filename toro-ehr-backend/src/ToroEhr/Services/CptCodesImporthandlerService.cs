using Coravel.Invocable;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using ToroEhr.Domain;
using ToroEhr.Infrastructure;
using System.IO.Compression;
using ClosedXML.Excel;
using System.Text;
using System.Text.Json;

namespace ToroEhr.Services;

public class CptCodesImporthandlerService : IInvocable, ICancellableInvocable
{
    private readonly IDocumentStore _store;
    private readonly HttpClient _httpClient;
    private readonly SlackService _slackService;

    private readonly string _authHeader;

    public CancellationToken CancellationToken { get; set; }

    public CptCodesImporthandlerService(IDocumentStore store, HttpClient httpClient, SlackService slackService)
    {
        _store = store;
        _httpClient = httpClient;
        _slackService = slackService;
        _authHeader = GetAuthHeader();
    }
    public async Task Invoke()
    {
        if (CancellationToken.IsCancellationRequested) return;
        await Process(CancellationToken);
    }

    private async Task Process(CancellationToken cancellationToken)
    {
        try
        {
            var token = await SendAuthRequest();
            HttpResponseMessage response = await DownloadRequest(token);

            var cptCodes = new List<CptCode>();

            await using (Stream zipStream = await response.Content.ReadAsStreamAsync())
            using (ZipArchive archive = new ZipArchive(zipStream, ZipArchiveMode.Read))
            {
                ZipArchiveEntry? excelEntry = archive.Entries
                    .FirstOrDefault(e => e.Name.Equals("ConsumerDescriptor.xlsx", StringComparison.OrdinalIgnoreCase));

                if (excelEntry == null)
                {
                    Console.WriteLine("Excel file not found in ZIP.");
                    return;
                }
                using (Stream excelStream = excelEntry.Open())
                using (XLWorkbook workbook = new XLWorkbook(excelStream))
                {
                    var sheetName = "Sheet0";
                    var worksheet = workbook.Worksheet(sheetName);
                    if (worksheet == null)
                    {
                        throw new Exception($"Sheet '{sheetName}' not found in the Excel file.");
                    }

                    int startRow = 2;

                    foreach (var row in worksheet.RowsUsed().Skip(startRow - 1))
                    {
                        var data = CptCode.Create(
                            code: row.Cell(2).GetValue<string>(), // Column B
                            conceptId: row.Cell(1).GetValue<string>(), // Column A
                            clinicianDescriptor: row.Cell(3).GetValue<string>() // Column C
                        );

                        cptCodes.Add(data);
                    }
                }

                using IAsyncDocumentSession session = _store.OpenAsyncSession();

                foreach (var cptCode in cptCodes)
                {
                    await session.StoreAsync(cptCode, cancellationToken);
                }

                await session.SaveChangesAsync(cancellationToken);
            }


            await _slackService.SendMessageAsync($"The new CPT codes list has been successfully imported!\nDate: {DateTime.UtcNow:g}");

        }
        catch (Exception exc)
        {
            await _slackService.SendMessageAsync($"Data import failed!\n" +
                           $"Date: {DateTime.UtcNow.ToString("g")}\n" +
                           FormatExceptionDetails(exc));
        }
    }

    private async Task<HttpResponseMessage> DownloadRequest(string token)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, "cpt-zip/1.0.0/files");
        request.Headers.Add("Authorization", $"Bearer {token}");

        HttpResponseMessage response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        return response;
    }

    private async Task<string> SendAuthRequest()
    {
        var request = new HttpRequestMessage(HttpMethod.Post, "token");
        request.Headers.Add("Authorization", _authHeader);

        var content = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("grant_type", "client_credentials")
        });

        request.Content = content;


        HttpResponseMessage response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();

        string responseContent = await response.Content.ReadAsStringAsync();

        using var jsonDoc = JsonDocument.Parse(responseContent);
        string accessToken = jsonDoc.RootElement.GetProperty("access_token").GetString()!;

        return accessToken;
    }

    private string GetAuthHeader()
    {
        string credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Config.Ama.AmaClientId}:{Config.Ama.AmaClientSecret}"));
        return $"Basic {credentials}";
    }

    private static string FormatExceptionDetails(Exception exc) => $"Message: {exc.Message}\nStack trace: {exc.StackTrace}";
}