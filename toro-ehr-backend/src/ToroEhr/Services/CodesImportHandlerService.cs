using Coravel.Invocable;
using Raven.Client.Documents.Session;
using Raven.Client.Documents;
using System.Text;
using System.Xml.Linq;
using ToroEhr.Infrastructure;
using ToroEhr.Domain;

namespace ToroEhr.Services;

public class CodesImportHandlerService : IInvocable, ICancellableInvocable
{
    private readonly IDocumentStore _store;
    private readonly HttpClient _httpClient;
    private readonly SlackService _slackService;

    private readonly string _authHeader;

    public CancellationToken CancellationToken { get; set; }

    public CodesImportHandlerService(IDocumentStore store, HttpClient httpClient, SlackService slackService)
    {
        _store = store;
        _httpClient = httpClient;
        _slackService = slackService;
        _authHeader = GetAuthHeader();
    }
    public async Task Invoke()
    {
        if (CancellationToken.IsCancellationRequested) return;
        await Process(CancellationToken);
    }

    private async Task Process(CancellationToken cancellationToken)
    {
        try
        {
            using IAsyncDocumentSession session = _store.OpenAsyncSession();

            //await ImportData(session, "2.16.840.1.113762.1.4.1010.4", Medication.Create, cancellationToken);
            await ImportData(session, "2.16.840.1.113762.1.4.1186.8", Allergie.Create, cancellationToken);
            await ImportData(session, "2.16.840.1.113762.1.4.1010.6", Immunization.Create, cancellationToken);
        }
        catch (Exception exc)
        {
            await _slackService.SendMessageAsync($"Data import failed!\n" +
                           $"Date: {DateTime.UtcNow.ToString("g")}\n" +
                           FormatExceptionDetails(exc));
        }      
    }

    private async Task ImportData<T>(IAsyncDocumentSession session, string oid, Func<string, string, string, string, string, T> createEntity, 
        CancellationToken cancellationToken)
    {
        var url = $"RetrieveMultipleValueSets?id={oid}";
        HttpResponseMessage response = await SendHttpRequest(url);
        List<CodingRecord> records = ParseConceptList(await response.Content.ReadAsStringAsync());

        foreach (var record in records)
        {
            await session.StoreAsync(createEntity(record.Code, record.CodeSystem, record.CodeSystemName, record.CodeSystemVersion, record.DisplayName), cancellationToken);
        }

        await session.SaveChangesAsync(cancellationToken);
        await _slackService.SendMessageAsync($"The new {nameof(T)} list has been successfully imported!\nDate: {DateTime.UtcNow:g}");
    }

    private async Task<HttpResponseMessage> SendHttpRequest(string url)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, url);
        request.Headers.Add("Authorization", _authHeader);
        HttpResponseMessage response = await _httpClient.SendAsync(request);
        response.EnsureSuccessStatusCode();
        return response;
    }

    private string GetAuthHeader()
    {
        string credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($":{Config.Umls.UmlsApiKey}"));
        return $"Basic {credentials}";
    }

    private static List<CodingRecord> ParseConceptList(string xmlString)
    {
        XDocument doc = XDocument.Parse(xmlString);
        XNamespace ns = "urn:ihe:iti:svs:2008";
        return doc.Descendants(ns + "Concept").Select(concept => new CodingRecord
        {
            Code = concept.Attribute("code")?.Value ?? string.Empty,
            CodeSystem = concept.Attribute("codeSystem")?.Value ?? string.Empty,
            CodeSystemName = concept.Attribute("codeSystemName")?.Value ?? string.Empty,
            CodeSystemVersion = concept.Attribute("codeSystemVersion")?.Value ?? string.Empty,
            DisplayName = concept.Attribute("displayName")?.Value ?? string.Empty
        }).ToList();
    }

    private static string FormatExceptionDetails(Exception exc) => $"Message: {exc.Message}\nStack trace: {exc.StackTrace}";
}

public class CodingRecord
{
    public string Code { get; set; } = string.Empty;
    public string CodeSystem { get; set; } = string.Empty;
    public string CodeSystemName { get; set; } = string.Empty;
    public string CodeSystemVersion { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
}