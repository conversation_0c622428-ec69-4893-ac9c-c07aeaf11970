using SendGrid;
using SendGrid.Helpers.Mail;
using System.IO;
using ToroEhr.Infrastructure;
using ToroEhr.Shared;

namespace ToroEhr.Services;

public class EmailService
{
    private readonly S3FileService _fileService;

    public EmailService(S3FileService fileService)
    {
        _fileService = fileService;
    }
    public async virtual Task SendEmailAsync(string toEmail, string subject, string message, 
        List<string>? attachments = null, string? replyToEmail = null)
    {
        var client = new SendGridClient(Config.SendGrid.ApiKey);
        var from = new EmailAddress("<EMAIL>", "Toro Health");
        var to = new EmailAddress(toEmail);

        var msg = MailHelper.CreateSingleEmail(from, to, subject, "", message);

        if (replyToEmail.IsNotNullOrWhiteSpace())
        {
            msg.ReplyTo = new EmailAddress(replyToEmail, "Toro Health");
        }

        if (attachments != null && attachments.Any())
        {
            foreach (var fileUrl in attachments)
            {
                MemoryStream fileMemoryStream = (MemoryStream)await _fileService.DownloadFile(Config.S3.AppFilesBucketName, fileUrl);
                var fileBytes = fileMemoryStream.ToArray();
                string base64File = Convert.ToBase64String(fileBytes);

                // Optionally get filename from URL
                var fileName = Path.GetFileName(fileUrl);

                msg.AddAttachment(fileName, base64File);
            }
        }

        var response = await client.SendEmailAsync(msg);
    }
}