using Coravel.Invocable;
using MediatR;
using Raven.Client.Documents;
using ToroEhr.Features.Shared;

namespace ToroEhr.Services;

public class EventHandlerService : IInvocable, ICancellableInvocable
{
    private readonly IDocumentStore _store;
    private readonly IMediator _mediator;

    public CancellationToken CancellationToken { get; set; }

    public EventHandlerService(IDocumentStore store, IMediator mediator)
    {
        _store = store;
        _mediator = mediator;
    }
    public async Task Invoke()
    {
        if (CancellationToken.IsCancellationRequested) return;
        await Process(CancellationToken);
    }

    private async Task Process(CancellationToken cancellationToken)
    {
        using var session = _store.OpenAsyncSession();

        var unprocessedEvents = await session.Query<BaseEventEntity>(collectionName: "Events")
            .Where(@event => @event.ProcessedOn == null)
            .ToListAsync(cancellationToken);

        foreach (var @event in unprocessedEvents)
        {
            try
            {
                await _mediator.Publish(@event, cancellationToken);
                //Delete event record from DB after successful handling
                session.Delete(@event);
            }
            catch (Exception exc)
            {
                @event.ExceptionDetails = FormatExceptionDetails(exc);
                @event.ProcessedOn = DateTime.UtcNow;
                //todo: send slack message
            }
        }

        await session.SaveChangesAsync(cancellationToken);
    }

    private static string FormatExceptionDetails(Exception exc)
    {
        var message = $"Message: {exc.Message}\n";
        message += $"Stack trace: {exc.StackTrace}";
        return message;
    }
}
