<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <targets>
    <target xsi:type="AsyncWrapper" name="Raven_Default_Target">
      <target
        name="FileTarget"
        xsi:type="File"
        createDirs="true"
        fileName="${basedir}/Logs/${shortdate}.log"
        archiveNumbering="DateAndSequence"
        header="Date|Level|ThreadID|Resource|Component|Logger|Message|Data"
        layout="${longdate:universalTime=true}|${level:uppercase=true}|${threadid}|${event-properties:item=Resource}|${event-properties:item=Component}|${logger}|${message:withexception=true}|${event-properties:item=Data}"
        footer="Date|Level|Resource|Component|Logger|Message|Data"
        concurrentWrites="false"
        writeFooterOnArchivingOnly="true"
        archiveAboveSize="134217728"
        enableArchiveFileCompression="true" />
    </target>
    <target xsi:type="AsyncWrapper" name="Raven_Default_Audit_Target">
      <target
        name="FileTarget"
        xsi:type="File"
        createDirs="true"
        fileName="${basedir}/Logs/${shortdate}.audit.log"
        archiveNumbering="DateAndSequence"
        header="Date|Level|ThreadID|Resource|Component|Logger|Message|Data"
        layout="${longdate:universalTime=true}|${level:uppercase=true}|${threadid}|${event-properties:item=Resource}|${event-properties:item=Component}|${logger}|${message:withexception=true}|${event-properties:item=Data}"
        footer="Date|Level|Resource|Component|Logger|Message|Data"
        concurrentWrites="false"
        writeFooterOnArchivingOnly="true"
        archiveAboveSize="134217728"
        maxArchiveDays="3"
        enableArchiveFileCompression="false" />
    </target>
  </targets>
  <rules>
    <!--Those loggers are mandatory to be defined-->
    <logger ruleName="Raven_System" name="System.*" finalMinLevel="Warn" />
    <logger ruleName="Raven_Microsoft" name="Microsoft.*" finalMinLevel="Warn" />
    <logger ruleName="Raven_Default_Audit" name="Audit" levels="Info" final="true" />
    <logger ruleName="Raven_Default" name="*" levels="Info,Warn,Error,Fatal" writeTo="Raven_Default_Target" />
  </rules>
</nlog>
