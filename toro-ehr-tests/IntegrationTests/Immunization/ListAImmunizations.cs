using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace IntegrationTests.Immunization;

[Trait("List Immunizations", "Happy Path")]
public class ListImmunizationsHappyPath : IClassFixture<ListImmunizationsFixture>
{
    private readonly ListImmunizationsFixture _fixture;

    public ListImmunizationsHappyPath(ListImmunizationsFixture fixture)
    {

        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed Immunizations has expected content")]
    public void ExpectedContent()
    {
        Assert.NotNull(_fixture.Result);
        Assert.NotEmpty(_fixture.Result.Items);
    }
}

public class ListImmunizationsFixture : BaseFixture
{
    public PaginatedList<CodingResponse>? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await Session.StoreAsync(ToroEhr.Domain.Immunization.Create("10001", "Test", "Test 1", "1.0", "Test 1"));
        await Session.StoreAsync(ToroEhr.Domain.Immunization.Create("10002", "Test", "Test 1", "1.0", "Test 2"));

        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync("/immunizations");
        Result = await GetRequestContent<PaginatedList<CodingResponse>>(Response);
    }
}