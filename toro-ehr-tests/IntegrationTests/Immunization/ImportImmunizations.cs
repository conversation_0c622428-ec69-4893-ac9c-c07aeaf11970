using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Organization;

namespace IntegrationTests.Immunization;

[Trait("Import Immunizations", "Happy Path")]
public class ImportImmunizationsHappyPath : IClassFixture<ImportImmunizationsFixture>
{
    private readonly ImportImmunizationsFixture _fixture;

    public ImportImmunizationsHappyPath(ImportImmunizationsFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Immunizations has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Immunization> immunizations =
            await session.Query<ToroEhr.Domain.Immunization>()
            .ToListAsync();

        immunizations.ShouldNotBeNull();
        immunizations.Count.ShouldBe(208);
    }
}

public class ImportImmunizationsFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateOrganizationCommand? CreateOrganizationCommand;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        string testFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Immunization", "testimmunizations.xlsx");
        using var fileStream = File.OpenRead(testFilePath);
        using var streamContent = new StreamContent(fileStream);

        using var formData = new MultipartFormDataContent
        {
            { streamContent, "file", "testimmunizations.xlsx" } // "file" is the form field name
        };

        Response = await client.PostAsync("immunizations", formData);
    }
}