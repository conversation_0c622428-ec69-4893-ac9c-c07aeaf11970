using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Shared.Models;
using ToroEhr.Shared;
using ToroEhr.Features.LoincCode;

namespace IntegrationTests.LoincCode;

[Trait("List Loinc codes", "Happy Path")]
public class ListLoincCodesHappyPath : IClassFixture<ListLoincCodesFixture>
{
    private readonly ListLoincCodesFixture _fixture;

    public ListLoincCodesHappyPath(ListLoincCodesFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed Loinc codes has expected content")]
    public void ExpectedContent()
    {
        Assert.NotNull(_fixture.Result);
        Assert.NotEmpty(_fixture.Result.Items);
    }
}

public class ListLoincCodesFixture : BaseFixture
{
    public PaginatedList<CodingResponse>? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await Session.StoreAsync(ToroEhr.Domain.LoincCode.Create(new LoincEntry
        {
            LoincNum = "12345-6",
            Component = "Glucose",
            Property = "Mass Concentration",
            TimeAspct = "Pt",
            System = "Blood",
            ScaleTyp = "Qn",
            MethodTyp = "Laboratory",
            Class = "CHEM",
            VersionLastChanged = "2.71",
            ChngType = "Update",
            DefinitionDescription = "Glucose measurement in blood",
            Status = "ACTIVE",
            ConsumerName = "Blood Glucose",
            Classtype = "1",
            Formula = "C6H12O6",
            ExmplAnswers = "90 mg/dL",
            SurveyQuestText = "What is your blood glucose level?",
            SurveyQuestSrc = "Patient Survey",
            UnitsRequired = "Y",
            RelatedNames2 = "Sugar, Glc, BG",
            ShortName = "Glucose Bld",
            OrderObs = "Observation",
            Hl7FieldSubfieldId = "OBX-5",
            ExternalCopyrightNotice = "© LOINC 2024",
            ExampleUnits = "mg/dL",
            LongCommonName = "Glucose [Mass/volume] in Blood",
            ExampleUcumUnits = "mg/dL",
            StatusReason = "Routine update",
            StatusText = "Active in latest version",
            ChangeReasonPublic = "Clarified component terminology",
            CommonTestRank = 10,
            CommonOrderRank = 20,
            Hl7AttachmentStructure = "N/A",
            ExternalCopyrightLink = "https://loinc.org",
            PanelType = "Basic Metabolic Panel",
            AskAtOrderEntry = "Fasting required?",
            AssociatedObservations = "HbA1c, Ketones",
            VersionFirstReleased = "1.0",
            ValidHl7AttachmentRequest = "Y",
            DisplayName = "Blood Glucose Test"
        }));
        await Session.StoreAsync(ToroEhr.Domain.LoincCode.Create(new LoincEntry
        {
            LoincNum = "67890-1",
            Component = "Hemoglobin",
            Property = "Mass Concentration",
            TimeAspct = "Pt",
            System = "Blood",
            ScaleTyp = "Qn",
            MethodTyp = "Automated count",
            Class = "HEM/BC",
            VersionLastChanged = "2.72",
            ChngType = "New",
            DefinitionDescription = "Measurement of hemoglobin concentration in blood",
            Status = "ACTIVE",
            ConsumerName = "Hemoglobin Level",
            Classtype = "1",
            Formula = "C2952H4664O832N812S8Fe4",
            ExmplAnswers = "13.5 g/dL",
            SurveyQuestText = "What is your hemoglobin level?",
            SurveyQuestSrc = "Lab Test",
            UnitsRequired = "Y",
            RelatedNames2 = "Hb, Hgb, Hemoglobin Blood",
            ShortName = "Hgb Bld",
            OrderObs = "Observation",
            Hl7FieldSubfieldId = "OBX-5",
            ExternalCopyrightNotice = "© LOINC 2024",
            ExampleUnits = "g/dL",
            LongCommonName = "Hemoglobin [Mass/volume] in Blood",
            ExampleUcumUnits = "g/dL",
            StatusReason = "Added for new reference range",
            StatusText = "Active in latest version",
            ChangeReasonPublic = "Aligned with latest clinical guidelines",
            CommonTestRank = 5,
            CommonOrderRank = 12,
            Hl7AttachmentStructure = "N/A",
            ExternalCopyrightLink = "https://loinc.org",
            PanelType = "Complete Blood Count",
            AskAtOrderEntry = "Recent blood transfusion?",
            AssociatedObservations = "Hematocrit, RBC count",
            VersionFirstReleased = "1.2",
            ValidHl7AttachmentRequest = "Y",
            DisplayName = "Hemoglobin Blood Test"
        }));

        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync("/loinc");
        Result = await GetRequestContent<PaginatedList<CodingResponse>>(Response);
    }
}