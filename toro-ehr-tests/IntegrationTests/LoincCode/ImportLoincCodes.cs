using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;

namespace IntegrationTests.LoincCode;

internal class ImportLoincCodes
{
}
[Trait("Import Loinc Codes", "Happy Path")]
public class ImportLoincCodesHappyPath : IClassFixture<ImportLoincCodesFixture>
{
    private readonly ImportLoincCodesFixture _fixture;

    public ImportLoincCodesHappyPath(ImportLoincCodesFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Loinc codes has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        var loincCodeNumber = await session.Query<ToroEhr.Domain.LoincCode>().CountAsync();

        loincCodeNumber.ShouldBe(104584);
    }
}

public class ImportLoincCodesFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        string testFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "LoincCode", "loinctest.csv");
        using var fileStream = File.OpenRead(testFilePath);
        using var streamContent = new StreamContent(fileStream);

        using var formData = new MultipartFormDataContent
        {
            { streamContent, "file", "loinctest.csv" } // "file" is the form field name
        };

        Response = await client.PostAsync("loinc", formData);
    }
}