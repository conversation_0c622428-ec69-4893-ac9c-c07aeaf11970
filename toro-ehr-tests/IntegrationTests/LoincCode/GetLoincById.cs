using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Features.LoincCode;

namespace IntegrationTests.LoincCode;

[Trait("Get Loinc By Id", "Happy Path")]
public class GetLoincByIdHappyPath : IClassFixture<GetLoincByIdFixture>
{
    private readonly GetLoincByIdFixture _fixture;

    public GetLoincByIdHappyPath(GetLoincByIdFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed Loinc codes has expected content")]
    public async Task ExpectedContent()
    {
        var settings = new VerifySettings();
        settings.IgnoreMembers("Id");
        await Verify(_fixture.Result, settings);
    }
}

public class GetLoincByIdFixture : BaseFixture
{
    public LoincCodeDetailsResponse? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        var loincCode = ToroEhr.Domain.LoincCode.Create(new LoincEntry
        {
            LoincNum = "12345-6",
            Component = "Glucose",
            Property = "Mass Concentration",
            TimeAspct = "Pt",
            System = "Blood",
            ScaleTyp = "Qn",
            MethodTyp = "Laboratory",
            Class = "CHEM",
            VersionLastChanged = "2.71",
            ChngType = "Update",
            DefinitionDescription = "Glucose measurement in blood",
            Status = "ACTIVE",
            ConsumerName = "Blood Glucose",
            Classtype = "1",
            Formula = "C6H12O6",
            ExmplAnswers = "90 mg/dL",
            SurveyQuestText = "What is your blood glucose level?",
            SurveyQuestSrc = "Patient Survey",
            UnitsRequired = "Y",
            RelatedNames2 = "Sugar, Glc, BG",
            ShortName = "Glucose Bld",
            OrderObs = "Observation",
            Hl7FieldSubfieldId = "OBX-5",
            ExternalCopyrightNotice = "© LOINC 2024",
            ExampleUnits = "mg/dL",
            LongCommonName = "Glucose [Mass/volume] in Blood",
            ExampleUcumUnits = "mg/dL",
            StatusReason = "Routine update",
            StatusText = "Active in latest version",
            ChangeReasonPublic = "Clarified component terminology",
            CommonTestRank = 10,
            CommonOrderRank = 20,
            Hl7AttachmentStructure = "N/A",
            ExternalCopyrightLink = "https://loinc.org",
            PanelType = "Basic Metabolic Panel",
            AskAtOrderEntry = "Fasting required?",
            AssociatedObservations = "HbA1c, Ketones",
            VersionFirstReleased = "1.0",
            ValidHl7AttachmentRequest = "Y",
            DisplayName = "Blood Glucose Test"
        });
        await Session.StoreAsync(loincCode);
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync($"/loinc/{loincCode.Id}");
        Result = await GetRequestContent<LoincCodeDetailsResponse>(Response);
    }
}