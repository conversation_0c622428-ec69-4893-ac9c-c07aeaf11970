using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Shared;
using ToroEhr.Features.CptCodes;

namespace IntegrationTests.CptCodes;

[Trait("List CPT Codes", "Happy Path")]
public class ListCptCodesHappyPath : IClassFixture<ListCptCodesFixture>
{
    private readonly ListCptCodesFixture _fixture;

    public ListCptCodesHappyPath(ListCptCodesFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed ICD 10 has expected content")]
    public void ExpectedContent()
    {
        Assert.NotNull(_fixture.Result);
        Assert.NotEmpty(_fixture.Result.Items);
    }
}

public class ListCptCodesFixture : BaseFixture
{
    public PaginatedList<CptCodeResponse>? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await Session.StoreAsync(ToroEhr.Domain.CptCode.Create("A1234", string.Empty, "Test 1"));
        await Session.StoreAsync(ToroEhr.Domain.CptCode.Create("B1234", string.Empty, "Test 2"));

        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync("/cptcodes");
        Result = await GetRequestContent<PaginatedList<CptCodeResponse>>(Response);
    }
}