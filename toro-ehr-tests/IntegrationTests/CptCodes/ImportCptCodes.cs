using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Organization;

namespace IntegrationTests.CptCodes;

[Trait("Import ICD 10", "Happy Path")]
public class ImportCptCodesHappyPath : IClassFixture<ImportCptCodesFixture>
{
    private readonly ImportCptCodesFixture _fixture;

    public ImportCptCodesHappyPath(ImportCptCodesFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. CPT Codes has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.CptCode> cptCodes =
            await session.Query<ToroEhr.Domain.CptCode>()
            .ToListAsync();

        cptCodes.ShouldNotBeNull();
        cptCodes.Count.ShouldBe(10743);
    }
}

public class ImportCptCodesFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateOrganizationCommand? CreateOrganizationCommand;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        string testFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CptCodes", "testcptcodes.xlsx");
        using var fileStream = File.OpenRead(testFilePath);
        using var streamContent = new StreamContent(fileStream);

        using var formData = new MultipartFormDataContent
        {
            { streamContent, "file", "testcptcodes.xlsx" } // "file" is the form field name
        };

        Response = await client.PostAsync("cptcodes", formData);
    }
}