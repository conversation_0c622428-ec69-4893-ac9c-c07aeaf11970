using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Employee;

namespace IntegrationTests.Employee;

[Trait("Edit Location Employee Office Hours", "Happy Path")]
public class EditLocationEmployeeOfficeHoursHappyPath : IClassFixture<EditLocationEmployeeOfficeHoursFixture>
{
    private readonly EditLocationEmployeeOfficeHoursFixture _fixture;

    public EditLocationEmployeeOfficeHoursHappyPath(EditLocationEmployeeOfficeHoursFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Response has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        ToroEhr.Domain.LocationEmployee matchedOrgPra =
            await session.Query<ToroEhr.Domain.LocationEmployee>()
            .Where(x => x.EmployeeId == _fixture.EmployeeDoctor!.Id && x.OrganizationId == _fixture.Organization!.Id)
            .FirstOrDefaultAsync();

        matchedOrgPra.ShouldNotBeNull();
        matchedOrgPra.OfficeHours.Count.ShouldBe(_fixture.EditOfficeHoursRequest!.OfficeHours.Count);
    }
}

public class EditLocationEmployeeOfficeHoursFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public EditOfficeHoursRequest? EditOfficeHoursRequest;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        // when
        HttpClient client = CreateEmployeeDoctorClient();
        EditOfficeHoursRequest = new EditOfficeHoursRequest(new List<ToroEhr.ValueObjects.OfficeHours>
        {
            new ToroEhr.ValueObjects.OfficeHours(DayOfWeek.Monday, new TimeSpan(9, 0, 0), new TimeSpan(17, 0, 0), new List<ToroEhr.ValueObjects.Exclusion>
            {
                new ToroEhr.ValueObjects.Exclusion("lunch", new TimeSpan(13, 0, 0),  new TimeSpan(14, 0, 0))
            }),
        });
        StringContent requestContent = BuildRequestContent(EditOfficeHoursRequest);
        Response = await client.PutAsync($"employees/{EmployeeDoctor.Id}/locations/{Location.Id}/office-hours", requestContent);
    }
}