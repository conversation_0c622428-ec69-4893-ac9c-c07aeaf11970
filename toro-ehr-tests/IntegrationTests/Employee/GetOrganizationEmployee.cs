using IntegrationTests.Infrastructure;
using Shouldly;
using System.Net;
using ToroEhr.Features.Employee;

namespace IntegrationTests.Employee;


[Trait("Get Location Employee", "Happy Path")]
public class GetLocationEmployeeHappyPath : IClassFixture<GetLocationEmployeeFixture>
{
    private readonly GetLocationEmployeeFixture _fixture;

    public GetLocationEmployeeHappyPath(GetLocationEmployeeFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Location Employee has expected content")]
    public async Task ExpectedContent()
    {
        await Verify(_fixture.LocationEmployeeResponse);
    }
}

public class GetLocationEmployeeFixture : BaseFixture
{
    public LocationEmployeeResponse? LocationEmployeeResponse;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {        
        // given
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        // when
        HttpClient client = CreateEmployeeDoctorClient();
        Response = await client.GetAsync($"/employees/{EmployeeDoctor.Id}/locations/{Location.Id}");
        LocationEmployeeResponse = await GetRequestContent<LocationEmployeeResponse>(Response);
    }
}