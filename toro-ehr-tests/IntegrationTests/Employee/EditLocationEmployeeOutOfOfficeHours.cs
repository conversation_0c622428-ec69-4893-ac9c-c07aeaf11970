using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Employee;

namespace IntegrationTests.Employee;

[Trait("Edit Location Employee Out of Office Hours", "Happy Path")]
public class EditLocationEmployeeOutOfOfficeHoursHappyPath : IClassFixture<EditLocationEmployeeOutofOfficeHoursFixture>
{
    private readonly EditLocationEmployeeOutofOfficeHoursFixture _fixture;

    public EditLocationEmployeeOutOfOfficeHoursHappyPath(EditLocationEmployeeOutofOfficeHoursFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Response has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        ToroEhr.Domain.LocationEmployee matchedOrgPra =
            await session.Query<ToroEhr.Domain.LocationEmployee>()
            .Where(x => x.EmployeeId == _fixture.EmployeeDoctor!.Id && x.OrganizationId == _fixture.Organization!.Id)
            .FirstOrDefaultAsync();

        matchedOrgPra.ShouldNotBeNull();
        matchedOrgPra.OutOfOfficeHours.ShouldBe(_fixture.EditOutOfOfficeHoursRequest!.OutOfOfficeHoursList);
    }
}

public class EditLocationEmployeeOutofOfficeHoursFixture : BaseFixture
{
    public HttpResponseMessage? Response;
    public EditOutOfOfficeHoursRequest? EditOutOfOfficeHoursRequest;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        // when
        HttpClient client = CreateEmployeeDoctorClient();
        EditOutOfOfficeHoursRequest = new EditOutOfOfficeHoursRequest(new List<ToroEhr.ValueObjects.OutOfOfficeHours>
        {
            new ToroEhr.ValueObjects.OutOfOfficeHours(DateTime.UtcNow, DateTimeOffset.UtcNow.AddDays(1))
        });
        StringContent requestContent = BuildRequestContent(EditOutOfOfficeHoursRequest);
        Response = await client.PutAsync($"employees/{EmployeeDoctor.Id}/locations/{Location.Id}/out-of-office-hours", requestContent);
    }
}