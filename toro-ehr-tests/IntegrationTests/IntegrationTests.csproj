<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsPackable>false</IsPackable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Alba" Version="8.1.1" />
        <PackageReference Include="coverlet.collector" Version="6.0.2" />
        <PackageReference Include="FakeItEasy" Version="8.3.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
        <PackageReference Include="RavenDB.TestDriver" Version="7.0.0" />
        <PackageReference Include="Shouldly" Version="4.3.0" />
        <PackageReference Include="Verify.Xunit" Version="28.13.0" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
    </ItemGroup>

    <ItemGroup>
        <Using Include="Xunit" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\toro-ehr-backend\src\ToroEhr\ToroEhr.csproj" />
    </ItemGroup>

    <ItemGroup>
      <None Update="Allergies\testallergies.xlsx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="CptCodes\testcptcodes.xlsx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Icd10\icd10testfile.txt">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Immunization\testimmunizations.xlsx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="LoincCode\loinctest.csv">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Medication\testmedications.xlsx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Patient\" />
    </ItemGroup>

</Project>
