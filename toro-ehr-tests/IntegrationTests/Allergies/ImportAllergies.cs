using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using System.Text;
using ToroEhr.Features.Organization;

namespace IntegrationTests.Allergies;

[Trait("Import Allergies", "Happy Path")]
public class ImportAllergiesHappyPath : IClassFixture<ImportAllergiesFixture>
{
    private readonly ImportAllergiesFixture _fixture;

    public ImportAllergiesHappyPath(ImportAllergiesFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Allergies has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Allergie> allergies =
            await session.Query<ToroEhr.Domain.Allergie>()
            .ToListAsync();

        allergies.ShouldNotBeNull();
        allergies.Count.ShouldBe(818);
    }
}

public class ImportAllergiesFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateOrganizationCommand? CreateOrganizationCommand;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        string testFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Allergies", "testallergies.xlsx");
        using var fileStream = File.OpenRead(testFilePath);
        using var streamContent = new StreamContent(fileStream);

        using var formData = new MultipartFormDataContent
        {
            { streamContent, "file", "testallergies.xlsx" } // "file" is the form field name
        };

        Response = await client.PostAsync("allergies", formData);
    }
}