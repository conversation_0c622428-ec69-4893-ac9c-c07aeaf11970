using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace IntegrationTests.Allergies;

[Trait("List Allergies", "Happy Path")]
public class ListAllergiesHappyPath : IClassFixture<ListAllergiesFixture>
{
    private readonly ListAllergiesFixture _fixture;

    public ListAllergiesHappyPath(ListAllergiesFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed allergies has expected content")]
    public void ExpectedContent()
    {
        Assert.NotNull(_fixture.Result);
        Assert.NotEmpty(_fixture.Result.Items);
    }
}

public class ListAllergiesFixture : BaseFixture
{
    public PaginatedList<CodingResponse>? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await Session.StoreAsync(ToroEhr.Domain.Allergie.Create("10001", "Test", "Test 1", "1.0", "Test 1"));
        await Session.StoreAsync(ToroEhr.Domain.Allergie.Create("10002", "Test", "Test 1", "1.0", "Test 2"));

        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync("/allergies");
        Result = await GetRequestContent<PaginatedList<CodingResponse>>(Response);
    }
}