using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace IntegrationTests.Medication;

[Trait("List Medications", "Happy Path")]
public class ListMedicationsHappyPath : IClassFixture<ListMedicationsFixture>
{
    private readonly ListMedicationsFixture _fixture;

    public ListMedicationsHappyPath(ListMedicationsFixture fixture)
    {

        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed Medications has expected content")]
    public void ExpectedContent()
    {
        Assert.NotNull(_fixture.Result);
        Assert.NotEmpty(_fixture.Result.Items);
    }
}

public class ListMedicationsFixture : BaseFixture
{
    public PaginatedList<CodingResponse>? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        //await Session.StoreAsync(ToroEhr.Domain.Medication.Create("10001", "Test", "Test 1", "1.0", "Test 1"));
        //await Session.StoreAsync(ToroEhr.Domain.Medication.Create("10002", "Test", "Test 1", "1.0", "Test 2"));

        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync("/medications");
        Result = await GetRequestContent<PaginatedList<CodingResponse>>(Response);
    }
}