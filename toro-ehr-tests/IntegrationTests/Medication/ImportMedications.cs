using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Organization;

namespace IntegrationTests.Medication;

[Trait("Import Medications", "Happy Path")]
public class ImportMedicationsHappyPath : IClassFixture<ImportMedicationsFixture>
{
    private readonly ImportMedicationsFixture _fixture;

    public ImportMedicationsHappyPath(ImportMedicationsFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Medications has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Medication> medications =
            await session.Query<ToroEhr.Domain.Medication>()
            .ToListAsync();

        medications.ShouldNotBeNull();
        medications.Count.ShouldBe(79178);
    }
}

public class ImportMedicationsFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateOrganizationCommand? CreateOrganizationCommand;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        string testFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Medication", "testmedications.xlsx");
        using var fileStream = File.OpenRead(testFilePath);
        using var streamContent = new StreamContent(fileStream);

        using var formData = new MultipartFormDataContent
        {
            { streamContent, "file", "testmedications.xlsx" } // "file" is the form field name
        };

        Response = await client.PostAsync("medications", formData);
    }
}