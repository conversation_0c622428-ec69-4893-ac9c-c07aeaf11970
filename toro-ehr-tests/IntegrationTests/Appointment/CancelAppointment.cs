using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("Cancel Appointment", "Happy Path")]
public class CancelAppointmentHappyPath : IClassFixture<CancelAppointmentFixture>
{
    private readonly CancelAppointmentFixture _fixture;

    public CancelAppointmentHappyPath(CancelAppointmentFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Appointment is canceled, not deleted")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Appointment> appointments =
            await session.Query<ToroEhr.Domain.Appointment>()
            .ToListAsync();

        appointments.ShouldHaveSingleItem();
        appointments.First().Status.ShouldBe(AppointmentStatus.Canceled.Name);
    }

    [Fact(DisplayName = "3. Associated encounter is also canceled")]
    public async Task EncounterIsCanceled()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Encounter> encounters =
            await session.Query<ToroEhr.Domain.Encounter>()
            .ToListAsync();

        encounters.ShouldHaveSingleItem();
        encounters.First().Status.ShouldBe(EncounterStatus.Canceled.Name);
    }
}

public class CancelAppointmentFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CancelAppointmentCommand? Command;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        HttpClient client = CreatePatientClient();

        // Create encounter first
        var encounter = ToroEhr.Domain.Encounter.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, Location.Classification, DateTimeOffset.Now.AddDays(5));
        await Session.StoreAsync(encounter);

        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, encounter.Id,
            startAt: DateTimeOffset.Now.AddDays(5), endAt: DateTimeOffset.Now.AddDays(5).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        Command = new CancelAppointmentCommand(appointment.Id);
        StringContent requestContent = BuildRequestContent(Command);

        var request = new HttpRequestMessage(HttpMethod.Delete, "appointments")
        {
            Content = requestContent
        };

        Response = await client.SendAsync(request);

    }
}