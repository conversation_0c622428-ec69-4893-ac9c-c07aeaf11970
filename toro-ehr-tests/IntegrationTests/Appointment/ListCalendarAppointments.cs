using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("List Calendar Appointments", "Happy Path")]
public class ListCalendarAppointmentsHappyPath : IClassFixture<ListCalendarAppointmentsFixture>
{
    private readonly ListCalendarAppointmentsFixture _fixture;

    public ListCalendarAppointmentsHappyPath(ListCalendarAppointmentsFixture fixture)
    {

        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed appointment has expected content")]
    public async Task ExpectedContent()
    {
        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "EmployeeId", "LocationId");
        await Verify(_fixture.CalendarAppointmentResponse, settings);
    }
}

public class ListCalendarAppointmentsFixture : BaseFixture
{
    public List<CalendarAppointmentResponse>? CalendarAppointmentResponse;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        ToroEhr.Domain.Appointment appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, "missing encounter, fix",
            DateTimeOffset.UtcNow, DateTimeOffset.UtcNow.AddMinutes(30), 30, ToroEhr.Enums.AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        HttpClient client = CreateEmployeeDoctorClient();
        Response = await client.GetAsync("appointments/calendar-appointments");
        CalendarAppointmentResponse = await GetRequestContent<List<CalendarAppointmentResponse>>(Response);
    }
}