using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("Create Existing Patient Appointment", "Happy Path")]
public class CreateExistingPatientAppointmentHappyPath : IClassFixture<CreateExistingPatientAppointmentFixture>
{
    private readonly CreateExistingPatientAppointmentFixture _fixture;

    public CreateExistingPatientAppointmentHappyPath(CreateExistingPatientAppointmentFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Appointment has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Appointment> appointments =
            await session.Query<ToroEhr.Domain.Appointment>()
            .ToListAsync();

        appointments.ShouldNotBeEmpty();
        appointments.FirstOrDefault()?.EmployeeId.ShouldBe(_fixture.EmployeeDoctor.Id);
        appointments.FirstOrDefault()?.LocationId.ShouldBe(_fixture.Location.Id);
        appointments.FirstOrDefault()?.PatientId.ShouldBe(_fixture.Patient.Id);

        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "EmployeeId", "LocationId", "PatientId");
        await Verify(appointments, settings);
    }
}

public class CreateExistingPatientAppointmentFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateExistingPatientAppointmentCommand? Command;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        await Session.SaveChangesAsync();

        HttpClient client = CreatePatientClient();

        Command = new CreateExistingPatientAppointmentCommand(Patient.Id, EmployeeDoctor.Id, Location.Id, 30, DateTimeOffset.UtcNow.AddDays(1), true);
        StringContent requestContent = BuildRequestContent(Command);
        Response = await client.PostAsync("appointments/existing-patient", requestContent);
    }
}