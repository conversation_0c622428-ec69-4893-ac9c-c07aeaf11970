using System.Net;
using IntegrationTests.Infrastructure;
using ToroEhr.Features.Appointment;
using ToroEhr.Shared;

namespace IntegrationTests.Appointment;

[Trait("List Appointments", "Happy Path")]
public class ListAppointmentsHappyPath : IClassFixture<ListAppointmentsHappyPathFixture>
{
    private readonly ListAppointmentsHappyPathFixture _fixture;

    public ListAppointmentsHappyPath(ListAppointmentsHappyPathFixture fixture)
    {
        
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }
    
    [Fact(DisplayName = "2. Listed appointment has expected content")]
    public async Task ExpectedContent()
    {
        Assert.NotNull(_fixture.AppointmentsResult);
        Assert.NotEmpty(_fixture.AppointmentsResult.Items);


        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "EmployeeId", "LocationId");
        await Verify(_fixture.AppointmentsResult, settings);
    }
}

public class ListAppointmentsHappyPathFixture : BaseFixture
{
    public PaginatedList<AppointmentResponse>? AppointmentsResult;
    public HttpResponseMessage? Response;
    
    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        HttpClient client = CreatePatientClient();
        await CreateEmployeeDoctorUserWithSession(Session);

        ToroEhr.Domain.Appointment appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, "missing encounter, fix",
            DateTimeOffset.UtcNow, DateTimeOffset.UtcNow.AddMinutes(30), 30, ToroEhr.Enums.AppointmentStatus.Confirmed);
        await Session.StoreAsync(appointment);
        await Session.SaveChangesAsync();

        Response = await client.GetAsync("/appointments");
        AppointmentsResult = await GetRequestContent<PaginatedList<AppointmentResponse>>(Response);
    }
}

