using IntegrationTests.Infrastructure;
using Shouldly;
using System.Net;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("Get Appointment By Id", "Happy Path")]
public class AppointmentByIdHappyPath : IClassFixture<AppointmentByIdFixture>
{
    private readonly AppointmentByIdFixture _fixture;

    public AppointmentByIdHappyPath(AppointmentByIdFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Appointment has expected content")]
    public async Task ExpectedContent()
    {
        _fixture.AppointmentDetailsResponse!.EmployeeId.ShouldBe(_fixture.EmployeeDoctor.Id);
        _fixture.AppointmentDetailsResponse!.LocationId.ShouldBe(_fixture.Location.Id);

        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "EmployeeId", "LocationId");
        await Verify(_fixture.AppointmentDetailsResponse, settings);
    }
}

public class AppointmentByIdFixture : BaseFixture
{
    public AppointmentDetailsResponse? AppointmentDetailsResponse;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);
        var appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, "missing encounter, fix",
            startAt: DateTimeOffset.Now.AddDays(1), endAt: DateTimeOffset.Now.AddDays(1).AddMinutes(30), 30, AppointmentStatus.Confirmed);

        await Session.StoreAsync(appointment);

        await Session.SaveChangesAsync();

        // when
        HttpClient client = CreateEmployeeDoctorClient();
        Response = await client.GetAsync($"/appointments/{appointment.Id}");
        AppointmentDetailsResponse = await GetRequestContent<AppointmentDetailsResponse>(Response);
    }
}