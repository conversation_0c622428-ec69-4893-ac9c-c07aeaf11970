using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using ToroEhr.Domain;
using ToroEhr.Enums;
using ToroEhr.Features.Appointment;

namespace IntegrationTests.Appointment;

[Trait("Edit Appointment", "Happy Path")]
public class EditAppointmentHappyPath : IClassFixture<EditAppointmentFixture>
{
    private readonly EditAppointmentFixture _fixture;

    public EditAppointmentHappyPath(EditAppointmentFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Appointment has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Appointment> appointments =
            await session.Query<ToroEhr.Domain.Appointment>()
            .ToListAsync();

        appointments.ShouldNotBeEmpty();
        appointments.FirstOrDefault()?.Id.ShouldBe(_fixture.Appointment.Id);
        appointments.FirstOrDefault()?.EmployeeId.ShouldBe(_fixture.EmployeeDoctor.Id);
        appointments.FirstOrDefault()?.LocationId.ShouldBe(_fixture.Location.Id);
        appointments.FirstOrDefault()?.PatientId.ShouldBe(_fixture.Patient.Id);

        var settings = new VerifySettings();
        settings.IgnoreMembers("Id", "EmployeeId", "LocationId", "PatientId");
        await Verify(appointments, settings);
    }
}

public class EditAppointmentFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public EditAppointmentCommand? Command;
    public ToroEhr.Domain.Appointment Appointment;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();
        await CreateEmployeeDoctorUserWithSession(Session);

        Appointment = ToroEhr.Domain.Appointment.Create(Patient.Id, EmployeeDoctor.Id, Location.Id, "missing encounter, fix",
            startAt: DateTimeOffset.Now.AddDays(1), endAt: DateTimeOffset.Now.AddDays(1).AddMinutes(30), 30, AppointmentStatus.Confirmed);
        await Session.StoreAsync(Appointment);

        await Session.SaveChangesAsync();

        HttpClient client = CreatePatientClient();

        Command = new EditAppointmentCommand(Appointment.Id, EmployeeDoctor.Id, Location.Id, DateTimeOffset.UtcNow.AddDays(2), 30);
        StringContent requestContent = BuildRequestContent(Command);
        Response = await client.PutAsync("appointments", requestContent);
    }
}