using System.Net;
using IntegrationTests.Infrastructure;
using Shouldly;
using ToroEhr.Features.Organization;
using ToroEhr.Shared;

namespace IntegrationTests.Organization;

[Trait("List Organizations", "Happy Path")]
public class ListOrganizationsHappyPath : IClassFixture<ListOrganizationFixture>
{
    private readonly ListOrganizationFixture _fixture;

    public ListOrganizationsHappyPath(ListOrganizationFixture fixture) =>
        _fixture = fixture;
    
    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }
    
    [Fact(DisplayName = "2. Organization has expected content")]
    public void ExpectedContent()
    {
        _fixture.OrganizationsResult.ShouldNotBeNull();
        _fixture.OrganizationsResult.Items.ShouldNotBeEmpty();
        _fixture.OrganizationsResult.Items.First().Name.ShouldBe("Vitalis");
    }
}

public class ListOrganizationFixture : BaseFixture
{
    public PaginatedList<OrganizationResponse>? OrganizationsResult;
    public HttpResponseMessage? Response;
    
    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();
        
        // when
        HttpClient client = CreateSuperAdminClient();
        Response = await client.GetAsync("/organizations");
        OrganizationsResult = await GetRequestContent<PaginatedList<OrganizationResponse>>(Response);
    }
}