using System.Net;
using IntegrationTests.Infrastructure;
using Raven.Client.Documents.Session;
using Shouldly;
using ToroEhr.Features.Organization;

namespace IntegrationTests.Organization;

[Trait("Create Organizations", "Happy Path")]
public class CreateOrganizationHappyPath : IClassFixture<CreateOrganizationFixture>
{
    private readonly CreateOrganizationFixture _fixture;

    public CreateOrganizationHappyPath(CreateOrganizationFixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. Organization has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        ToroEhr.Domain.Organization matchedOrg =
            await session.LoadAsync<ToroEhr.Domain.Organization>(_fixture.CreateOrgResult);

        matchedOrg.ShouldNotBeNull();
        matchedOrg.Name.ShouldBe(_fixture.CreateOrganizationCommand!.OrganizationName);
    }
}

public class CreateOrganizationFixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateOrganizationCommand? CreateOrganizationCommand;

    public override async Task InitializeAsync()
    {
        // given
        await base.InitializeAsync();

        // when
        HttpClient client = CreateSuperAdminClient();
        CreateOrganizationCommand = new CreateOrganizationCommand("TestOrg", "TestLoc", 
            new OrganizationAdminRequest("Test", "Admin", "<EMAIL>"));
        StringContent requestContent = BuildRequestContent(CreateOrganizationCommand);

        Response = await client.PostAsync("organizations", requestContent);
        CreateOrgResult = await GetRequestContent<string>(Response);
    }
}