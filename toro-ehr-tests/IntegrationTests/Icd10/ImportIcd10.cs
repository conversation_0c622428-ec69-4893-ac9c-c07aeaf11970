using IntegrationTests.Infrastructure;
using Raven.Client.Documents;
using Raven.Client.Documents.Session;
using Shouldly;
using System.Net;
using System.Text;
using ToroEhr.Features.Organization;

namespace IntegrationTests.Icd10;

[Trait("Import ICD 10", "Happy Path")]
public class ImportIcd10HappyPath : IClassFixture<ImportIcd10Fixture>
{
    private readonly ImportIcd10Fixture _fixture;

    public ImportIcd10HappyPath(ImportIcd10Fixture fixture) =>
        _fixture = fixture;

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        _fixture.Response.ShouldNotBeNull();
        _fixture.Response.StatusCode.ShouldBe(HttpStatusCode.OK);
    }

    [Fact(DisplayName = "2. ICD 10 has expected content")]
    public async Task ExpectedContent()
    {
        using IAsyncDocumentSession session = _fixture.DocumentStore.OpenAsyncSession();

        List<ToroEhr.Domain.Icd10> icd10Codes =
            await session.Query<ToroEhr.Domain.Icd10>()
            .ToListAsync();

        icd10Codes.ShouldNotBeNull();
        icd10Codes.Count.ShouldBe(9);
    }
}

public class ImportIcd10Fixture : BaseFixture
{
    public string? CreateOrgResult;
    public HttpResponseMessage? Response;
    public CreateOrganizationCommand? CreateOrganizationCommand;

    public override async Task InitializeAsync()
    {
        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        string testFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icd10", "icd10testfile.txt");
        using var fileStream = File.OpenRead(testFilePath);
        using var streamContent = new StreamContent(fileStream);

        using var formData = new MultipartFormDataContent
        {
            { streamContent, "file", "icd10testfile.txt" } // "file" is the form field name
        };

        Response = await client.PostAsync("icd10", formData);
    }
}