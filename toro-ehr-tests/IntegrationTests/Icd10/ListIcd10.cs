using IntegrationTests.Infrastructure;
using System.Net;
using ToroEhr.Shared;
using ToroEhr.Shared.Models;

namespace IntegrationTests.Icd10;

[Trait("List ICD 10", "Happy Path")]
public class ListIcd10HappyPath : IClassFixture<ListIcd10Fixture>
{
    private readonly ListIcd10Fixture _fixture;

    public ListIcd10HappyPath(ListIcd10Fixture fixture)
    {
        _fixture = fixture;
    }

    [Fact(DisplayName = "1. Status 200 is returned")]
    public void StatusCode()
    {
        Assert.NotNull(_fixture.Response);
        Assert.Equal(HttpStatusCode.OK, _fixture.Response.StatusCode);
    }

    [Fact(DisplayName = "2. Listed ICD 10 has expected content")]
    public void ExpectedContent()
    {
        Assert.NotNull(_fixture.Result);
        Assert.NotEmpty(_fixture.Result.Items);
    }
}

public class ListIcd10Fixture : BaseFixture
{
    public PaginatedList<CodingResponse>? Result;
    public HttpResponseMessage? Response;

    public override async Task InitializeAsync()
    {
        await Session.StoreAsync(ToroEhr.Domain.Icd10.Create("A1234", string.Empty, string.Empty, string.Empty, "Test 1"));
        await Session.StoreAsync(ToroEhr.Domain.Icd10.Create("B1234", string.Empty, string.Empty, string.Empty, "Test 2"));

        await base.InitializeAsync();

        HttpClient client = CreateSuperAdminClient();

        Response = await client.GetAsync("/icd10");
        Result = await GetRequestContent<PaginatedList<CodingResponse>>(Response);
    }
}